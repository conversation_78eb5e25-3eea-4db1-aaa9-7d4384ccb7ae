<?php
/**
 * Fired during plugin deactivation
 *
 * @link       www.redefiningtheweb.com
 * @since      1.0.0
 *
 * @package    Rtwwcpig_Woocommerce_Pdf_Invoice_Generator
 * @subpackage Rtwwcpig_Woocommerce_Pdf_Invoice_Generator/includes
 */
/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      2.5.0
 * @package    Rtwwcpig_Woocommerce_Pdf_Invoice_Generator
 * @subpackage Rtwwcpig_Woocommerce_Pdf_Invoice_Generator/includes
 * <AUTHOR> <<EMAIL>>
 */

class Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Deactivator
{
    public static function rtwwcpig_add_custom_page_deactivate()
    {
        $rtwwcpig_cstm_page_id = get_option('order_datails_pg');
        if($rtwwcpig_cstm_page_id){
            wp_delete_post($rtwwcpig_cstm_page_id,true);
        }


    }



}