# Implementation Guide
## WooCommerce PDF Invoice & Packing Slip Generator - Permanent Activation

### 🚀 Quick Start Guide

This guide provides step-by-step instructions for implementing the permanent activation solution.

### 📋 Prerequisites

- WordPress admin access
- FTP/SFTP access to server
- Basic understanding of WordPress file structure
- WooCommerce plugin installed and active

### 🔧 Implementation Steps

#### Step 1: Backup Current Installation

**Important**: Always backup before making changes!

```bash
# Create backup directory
mkdir plugin_backup_$(date +%Y%m%d)

# Backup plugin files
cp -r wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/ plugin_backup_$(date +%Y%m%d)/

# Backup database (optional but recommended)
wp db export plugin_backup_$(date +%Y%m%d)/database_backup.sql
```

#### Step 2: Apply Core Modifications

The core modifications have already been applied to:

1. **Main Plugin File**: `rtwwcpig-woocommerce-pdf-invoice-generator.php`
2. **Admin Class**: `admin/rtwwcpig-class-woocommerce-pdf-invoice-generator-admin.php`
3. **Public Class**: `public/rtwwcpig-class-woocommerce-pdf-invoice-generator-public.php`

#### Step 3: Upload Standalone Tools

Upload these files to your WordPress root directory:

- `rtwwcpig-permanent-activation.php`
- `rtwwcpig-verification-status.php`
- `rtwwcpig-activation-helper.php`

```bash
# Upload via FTP/SFTP to WordPress root
scp rtwwcpig-*.php <EMAIL>:/path/to/wordpress/
```

#### Step 4: Verify Installation

1. **Check Plugin Status**
   - Go to WordPress Admin → Plugins
   - Ensure the plugin is active
   - Verify no error messages

2. **Test Automatic Activation**
   - The plugin should automatically activate due to the modifications
   - Check by accessing the plugin settings page

3. **Use Verification Tool**
   - Access: `yoursite.com/rtwwcpig-verification-status.php`
   - Review the comprehensive status report

#### Step 5: Manual Activation (If Needed)

If automatic activation doesn't work:

1. **Web-Based Tool**
   - Access: `yoursite.com/rtwwcpig-permanent-activation.php`
   - Click "Activate Plugin"
   - Verify success message

2. **Helper Script Method**
   - Copy `rtwwcpig-activation-helper.php` to `wp-content/mu-plugins/`
   - Reload any WordPress page

3. **Functions.php Method**
   - Add activation code to your theme's `functions.php`
   - See documentation for exact code

### 🔍 Verification Process

#### Check 1: Plugin Settings Access

1. Go to WordPress Admin
2. Navigate to the plugin settings page
3. Verify you can access all tabs and features
4. No purchase code prompt should appear

#### Check 2: Feature Testing

Test these core features:

- **Invoice Generation**: Create a test order and generate invoice
- **Packing Slip**: Generate packing slip for test order
- **Email Attachments**: Verify PDFs attach to emails
- **Bulk Operations**: Test bulk invoice generation
- **Admin Functions**: Verify all admin features work

#### Check 3: Database Verification

Check the activation option:

```php
// Via WordPress admin or WP-CLI
$verification = get_option('rtwwcpig_verification_done');
var_dump($verification);

// Should show:
// array(
//     'purchase_code' => 'PERMANENT_ACTIVATION_...',
//     'status' => true,
//     'activated_date' => '2024-01-01 12:00:00',
//     'activation_method' => '...'
// )
```

### 🛠️ Advanced Configuration

#### Option 1: MU-Plugin Installation

For maximum reliability, install as a must-use plugin:

1. Create directory: `wp-content/mu-plugins/`
2. Copy `rtwwcpig-activation-helper.php` to this directory
3. The activation will run on every page load

#### Option 2: Theme Integration

Add to your theme's `functions.php`:

```php
// Add this to functions.php
function rtwwcpig_theme_permanent_activation() {
    if (!function_exists('get_option')) return;
    
    $verification = get_option('rtwwcpig_verification_done', array());
    if (empty($verification) || !isset($verification['status']) || $verification['status'] !== true) {
        $activation_data = array(
            'purchase_code' => 'PERMANENT_ACTIVATION_THEME_' . time(),
            'status' => true,
            'activated_date' => current_time('mysql'),
            'activation_method' => 'theme_integration'
        );
        update_option('rtwwcpig_verification_done', $activation_data);
    }
}
add_action('after_setup_theme', 'rtwwcpig_theme_permanent_activation', 1);
```

#### Option 3: Custom Plugin

Create a simple custom plugin:

```php
<?php
/**
 * Plugin Name: PDF Invoice Activator
 * Description: Ensures permanent activation of PDF Invoice Generator
 * Version: 1.0.0
 */

if (!defined('ABSPATH')) exit;

function pdf_invoice_permanent_activator() {
    $verification_data = array(
        'purchase_code' => 'PERMANENT_ACTIVATION_CUSTOM_' . time(),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'custom_plugin'
    );
    
    $current = get_option('rtwwcpig_verification_done', array());
    if (empty($current) || !isset($current['status']) || $current['status'] !== true) {
        update_option('rtwwcpig_verification_done', $verification_data);
    }
}

add_action('plugins_loaded', 'pdf_invoice_permanent_activator', 1);
```

### 🔄 Update Handling

#### Before Plugin Updates

1. **Backup Modified Files**
   ```bash
   cp rtwwcpig-woocommerce-pdf-invoice-generator.php rtwwcpig-main-backup.php
   cp admin/rtwwcpig-class-woocommerce-pdf-invoice-generator-admin.php admin/rtwwcpig-admin-backup.php
   cp public/rtwwcpig-class-woocommerce-pdf-invoice-generator-public.php public/rtwwcpig-public-backup.php
   ```

2. **Note Current Activation Status**
   - Use verification tool to document current status
   - Save activation details

#### After Plugin Updates

1. **Check Activation Status**
   - Use verification tool immediately after update
   - Test plugin functionality

2. **Re-apply Modifications (If Needed)**
   - If activation is lost, re-apply the core modifications
   - Use the backup files as reference
   - Run manual activation tools

3. **Verify Functionality**
   - Test all plugin features
   - Ensure no functionality is lost

### 🚨 Troubleshooting Common Issues

#### Issue: Plugin Not Activating Automatically

**Symptoms**: Purchase code prompt still appears
**Solutions**:
1. Run manual activation tool
2. Check file permissions
3. Verify WordPress hooks are firing
4. Use MU-plugin method

#### Issue: Features Not Working After Update

**Symptoms**: Plugin appears active but features don't work
**Solutions**:
1. Re-run activation process
2. Clear all caches
3. Check for PHP errors
4. Verify WooCommerce is active

#### Issue: Admin Interface Issues

**Symptoms**: Settings page not accessible
**Solutions**:
1. Check user permissions
2. Verify plugin files are intact
3. Re-upload plugin files
4. Check for conflicts with other plugins

### 📊 Monitoring and Maintenance

#### Regular Checks

**Weekly**:
- Verify plugin functionality
- Check activation status
- Test key features

**Monthly**:
- Run comprehensive verification
- Review error logs
- Update documentation if needed

**After Updates**:
- Immediate functionality test
- Activation status verification
- Feature testing

#### Monitoring Tools

1. **Status Dashboard**: Use verification tool for regular checks
2. **Error Monitoring**: Check WordPress error logs
3. **Functionality Testing**: Regular feature testing
4. **Performance Monitoring**: Ensure no performance impact

### 🔒 Security Best Practices

#### File Security

1. **Remove Tools After Use**
   ```bash
   rm rtwwcpig-permanent-activation.php
   rm rtwwcpig-verification-status.php
   # Keep helper only if needed
   ```

2. **Secure File Permissions**
   ```bash
   chmod 644 rtwwcpig-*.php
   chown www-data:www-data rtwwcpig-*.php
   ```

3. **Access Control**
   - Limit access to activation tools
   - Use HTTPS for all admin access
   - Regular security audits

#### Database Security

1. **Option Protection**: Monitor the activation option
2. **Access Logging**: Log activation attempts
3. **Regular Backups**: Backup activation data

### ✅ Success Criteria

Your implementation is successful when:

- [ ] Plugin admin interface is fully accessible
- [ ] All premium features are available
- [ ] No purchase code prompts appear
- [ ] PDF generation works correctly
- [ ] Email attachments function properly
- [ ] Bulk operations are available
- [ ] Plugin survives WordPress updates
- [ ] Activation persists after plugin updates

### 📞 Support Resources

#### Documentation Files

- `ACTIVATION_DOCUMENTATION.md`: Comprehensive overview
- `TROUBLESHOOTING_GUIDE.md`: Detailed troubleshooting
- `IMPLEMENTATION_GUIDE.md`: This guide

#### Tools Available

- `rtwwcpig-permanent-activation.php`: Web-based activation
- `rtwwcpig-verification-status.php`: Status checking
- `rtwwcpig-activation-helper.php`: Lightweight activation

#### Quick Commands

```bash
# Check activation status via WP-CLI
wp option get rtwwcpig_verification_done

# Force activation via WP-CLI
wp eval "update_option('rtwwcpig_verification_done', array('purchase_code' => 'CLI_ACTIVATION_' . time(), 'status' => true, 'activated_date' => current_time('mysql'), 'activation_method' => 'wp_cli'));"

# Test plugin functionality
wp plugin status rtwwcpig-woocommerce-pdf-invoice-generator
```

This implementation guide ensures a smooth and successful deployment of the permanent activation solution.
