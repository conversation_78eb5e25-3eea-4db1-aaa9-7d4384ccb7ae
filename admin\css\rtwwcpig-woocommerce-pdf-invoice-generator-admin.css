/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */
.error{
    border-color: red !important;
    color: red  !important;;
}
.rtwwcpig_twilio_link{
    color: #9f0000;
    border: none;
    padding: 6px 10px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: 0.2s linear all;
}
.rtwwcpig_sms_desc{
    font-size: 16px;
    font-style: italic;
    font-weight: bold;
    background-color: #ffa700a6;
    color: white;
    text-align: center;
    margin-top: 20px;
    padding: 8px;
}
.rtwwcpig_sms{
    margin-left: 15px;
    background-color: #9575CD;
    color: #ffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: 0.2s linear all;
}
.rtwwcpig_hide{
    display: none;
}
.rtwwcpig_success{
    border-color: green !important;
    color: green  !important;
    font-size: 16px;
}
.rtwwcpig_failed{
    border-color: red !important;
    color: red  !important;
    font-size: 16px;
}

.rtwwcpig_verify_wrapper {
    padding: 30px;
    max-width: 500px;
    margin: 40px auto;
    box-shadow: 2px 1px 16px rgba(0,0,0,0.16);
    background-color: #ffffff;
}
#rtwwcpig_verify label {
    display: block;
    font-size: 19px;
    font-weight: bold;
}
#rtwwcpig_verify a {
    font-size: 14px;
}
.rtwwcpig_verify_wrapper .rtwwcpig_purchase_code {
    background-color: #ffffff;
    display: block;
    width: 100%;
    border: 1px solid #efefef;
    margin-bottom: 15px;
    padding: 10px;
    margin-top: 10px;
}
#rtwwcpig_verify_code {
    margin-top: 20px;
    padding: 10px 35px;
    font-size: 16px;
}
 .rtwwcpig_hide_template{
    display: none;
}
.rtwwcpig_cmnt{
    color: red;
}
#rtwwcpig_img_btn img {
    width: 22px;
    height: 22px;
}
#rtwwcpig_wtrmrk_img {
    display: inline-block;
}
#rtwwcpig_bck_img{
    display: inline-block;
}
#rtwwcpig_image_url{
    display: inline-block;
}
.rtw_btn {
    box-sizing: border-box;
    border-style: solid;
    border-width: 1px;
    text-indent: unset;
    display: inline-block;
    padding: 0 !important;
    height: 2em !important;
    width: 2em;
    border-color: #ccc;
    background: #f7f7f7;
    box-shadow: 0 1px 0 #ccc;
    font-size: 13px;
    margin: 2px;
    vertical-align: middle;
}
.rtwwcpig_macros{
    width: 600px; 
    height: 200px; 
    background-color: fff;
}
.purchase_code{
    float: right;
    background-color: #2C115C;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: 0.2s linear all;
    display: inline-block;
}
.rtwwcpig_macros_1{
    float: left; 
    width: 160px;
}
.rtwwcpig_macros_2{
    float: left; 
    width: 150px;
}
.rtwwcpig_macros_3{
    float: left; 
    width: 150px;
}
.rtwwcpig_macros_4{
    float: left; 
    width: 130px;
}
.rtwwcpig_macros_5{
    float: left; 
    width: 130px;
}
 .rtwwcpig-help-wrapper {
    display: flex;
    flex-wrap: wrap;
}
.rtwwcpig-help-image {
    width: 100px;
    margin: 0 auto 40px;
}
.rtwwcpig-help-image img {
    max-width: 100%;
    width: auto;
}
.rtwwcpig-column {
    flex: 0 0 50%;
    padding-left: 1%;
    padding-right: 1%;
    text-align: center;
    margin: 20px 0;
}
.rtwwcpig-inner-content {
    padding: 40px 20px;
    width: 100%;
    box-shadow: 0 3px 3px -2px rgba(0,0,0,.2),0 3px 4px 0 rgba(0,0,0,.14),0 1px 8px 0 rgba(0,0,0,.12);
}
.rtwwcpig-column.rtwwcpig-faq-column {
    flex: 0 0 100%;
}
.rtwwcpig-faq-wrapper {
    text-align: left;
    font-size: 15px;
}
.rtwwcpig-faq-heading {
    font-size: 18px;
    margin: 0 0 20px 0;
    font-weight: bold;
    cursor: pointer;
    background-color: #f4f4f4;
    padding: 15px;
}
.rtwwcpig-faq-heading::before {
    content: "\f347";
    font-family: 'dashicons';
    vertical-align: middle;
    margin-right: 10px;
}
.rtwwcpig-faq-heading.active::before {
    content: "\f343";
}
.rtwwcpig-faq-desc {
    display: none;
    padding-bottom: 20px;
    padding-left: 42px;
    font-size: 16px;
    line-height: 1.7;
}
.rtwwcpig_read_more_btn_wrapper {
    text-align: center;
}
#rtwwcpig-faq-more-content{
    display: none;
}
.rtwwcpig-width-twenty-percent {
    width : 15% !important;
    text-align: center;
    font-weight: 600;
}
.rtwwcpig-help-section-heading {
    width: 100%;
    text-align: center;
    font-size: 35px;
    margin: 15px 0;
    padding: 10px;
    font-weight: 500;
    line-height: 35px;
}
/*footer*/
.rtwwcpig-footer {
    background-color: #dddddd;
    padding: 20px;
    text-align: center;
    margin-top: 30px;
}
.rtwwcpig-footer .rtwwcpig-button {
    margin-right: 20px;
    margin-top: 15px;
    display: inline-block;
}
.rtwwcpig-footer .rtwwcpig-button:hover{
    color: #ffffff;
}
.rtwwcpig-button {
    background-color: #9575CD;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: 0.2s linear all;
    display: inline-block;
}
.rtwwcpig-button:hover,.rtwwcpig-button:focus{
    color: #ffffff;
    opacity: 0.8;
}
.rtw-main-heading img {
    vertical-align: middle;
    width: 47px;
    height: 47px;
    margin-right: 10px;
}
.rtwwcpig_display_none.button.button-primary.rtwwcpig_btn {
    display:none;
}
.rtwwcpig_btn_wrap .rtwwcpig_btn {
    display: inline-block;
    height: auto;
    padding: 3px;
    white-space: normal;
    width: 123px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1.2;
}
.rtwwcpig_btn_wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.rtwwcpig_btn{
display: inline-block;
}
#rtwwcpig_btn_img{
    float: left; 
    margin-right: 10px;
}
#rtwwcpig_img_btn{
    max-width: 100%;
    width: auto
}
#rtwwcpig_image_url{
    line-height: 60px;
}
#rtwwcpig_bckgrnd_img{
    float: left; 
    margin-right: 10px;
}
#rtwwcpig_bckgrnd_img{
    width : 60px !important;
}
#rtwwcpig_bckgrnd_img_btn{
    max-width: 100%;
    width: auto
}
#rtwwcpig_watermark_img_backgrnd{
    width : 60px !important;
}
#rtwwcpig_watermark_img {
    max-width: 100%;
    width: auto;
}
#rtwwcpig_wtrmrk_img{
    line-height: 60px;
}
#rtwwcpig_bck_img{
    line-height: 60px;
}
.display_none {
    display:none;
}
.rtwwcpig_hidden_table{
    display: none;
}
.rtwwcpig_hide_wtrmrk{
    display: none;
}
.rtwwcpig_hide_img_wtrmrk{
    display: none;
}
#rtwwcpig_watermark_img_backgrnd{
    float: left; 
    margin-right: 10px;
}
#rtwwcpig_imgbckgrnd{
    line-height: 60px;
}
.tooltip {
  position: relative;
  display: inline-block;
}
.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color:#0085ba;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  /* Position the tooltip */
  position: absolute;
  z-index: 1;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}
#rtwwcpig_notice{
  color:red;
}
.rtwwcpig_suggetion{
  font-weight: bold;
  color: #1f8f0c;
}
 table.dataTable.no-footer{
    border: none !important;
}
.main-wrapper, .main-wrapper *{
    box-sizing: border-box;
}
.rtwwcpig-approve-request::after {
    font-family: Dashicons;
    font-weight: 400;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    text-indent: 0px;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "\f147";
    margin: 0px;
}
.rtwwcpig-cancel-request::after {
    font-family: Dashicons;
    font-weight: 400;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    text-indent: 0px;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "\f158";
    margin: 0px;
}
.rtwwcpig_bar_chart {
    width: 48%;
    height: auto;
    margin-top: 30px;
    padding: 20px;
    background-color: #fff;
    margin-left: 2%;
    box-shadow: 0 2px 28px rgba(0,0,0,.22);
    border-radius: 6px;
}
.rtwwcpig_bar_chart.rtwwcpig_bar_full_chart {
    width: 69%;
}
.rtwwcpig_bar_chart_wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-left: -20px;
}
.rtwwcpig_sub_heading {
    font-size: 30px;
    text-align: center;
    color: #676767;
    font-weight: normal;
    margin-bottom: 30px;
    line-height: 1.2;
    margin-top: 0;
}
/**
* All of the CSS for your admin-specific functionality should be
* included in this file.
*/
.rtw-edit-link {
    color: #000;
    text-decoration: none;
}
/*header heading*/
.rtw-button {
    background-color: #9575CD;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: 0.2s linear all;
}
.rtw-button:hover,.rtw-button:focus{
    transition: 0.2s linear all;
    opacity: 0.8;
}
.rtw-main-heading {
    background-color: #9575CD;
    padding: 20px !important;
    color: #ffffff;
    font-weight: 600 !important;
    margin: 0 -20px!important;
}
/*css header heading*/
/*navigation*/
.rtw-navigation-wrapper {
    display: flex;
    border: none;
    background-color: #fff;
    margin: 0 -20px;
    padding: 0;
    overflow-x: auto;
}
.rtw-navigation-wrapper .nav-tab {
    border: none;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    margin: 0;
    padding: 10px;
    background-color: #f3e2e2;
    border-right: 2px solid #e0cccc;
    color: #383434;
    position: relative;
    white-space: normal;
    overflow-wrap: break-word;
}
.rtwwcpig_tab_icon img{
    max-width: 100%;
    width: auto;
}
.rtwwcpig_tab_icon{
    width: 40px;
    margin: 0 auto;
}
.rtwwcpig_admin_wrapper select,
.rtwwcpig_admin_wrapper input[type="text"],
.rtwwcpig_admin_wrapper input[type="number"],
.rtwwcpig_admin_wrapper input[type="email"] ,
.rtwwcpig_admin_wrapper input[type="file"],
.rtwwcpig_admin_wrapper textarea{
    width: 320px;
    padding: 10px;
    height: auto;
    background-color: #ffffff;
    border: 1px solid #ddd;
}
.rtw-navigation-wrapper .nav-tab.nav-tab-active{
    background-color: #ffffff;
    color: #676767;
}
.rtw-navigation-wrapper .nav-tab.nav-tab-active::after {
    content: "";
    border-bottom: 2px solid #ed1919;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.rtw-navigation-wrapper .nav-tab:focus{
    outline: 0;
    box-shadow: none;
}
.rtw-navigation-wrapper .nav-tab span{
    display: block;
    width: 100%;
    text-align: center;
}
.rtw-navigation-wrapper .nav-tab:last-child{
    border-right: none;
}
/*end navigation*/
/*middle content*/
.main-wrapper {
    padding-right: 20px;
}
.main-wrapper a {
    color: #009688;
}
.rtwwcpig_notification {
    background: linear-gradient(to right,#8e54e9 0,#4776e6 100%) !important;
    padding: 13px;
    margin-top: 30px;
    color: #ffffff;
    font-size: 14px;
}
.rtwwcpig_notification .dashicons {
    float: right;
    cursor: pointer;
}
.box-column-wrapper {
    width: 28%;
    margin-left: 1%;
    margin-top: 10px;
}
.rtwwcpig_notification span {
    font-weight: bold;
}
.box-column {
    width: 45%;
    text-align: center;
    background-color: #fff;
    margin-left: 4%;
    margin-top: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 28px rgba(0,0,0,.22);
    display: inline-block;
    padding: 15px 10px;
    border-radius: 6px;
}
.box-column .dashicons {
    background-color: #f1f1f1;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    color: #676767;
}
.box-column h4 {
    font-size: 28px;
    margin: 25px 0;
}
.box-column h6 {
    color: #777777;
    font-size: 14px;
    margin: 0;
    font-style: italic;
}
.box-column.box-column-full {
    width: 96%;
    padding: 30px;
    background-color: #9575CD;
    color: #ffffff;
}
.box-column.box-column-full h6{
    color: #ffffff;
}
.rtw-referrals-column-content .rtw-referrals-header {
    background-color: #9575CD;
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;
}
.rtw-referrals-column-content .rtw-referrals-header h3 {
    margin: 0;
    color: #ffffff;
    font-weight: 600;
    font-size: 20px;
}
.rtw-nav a {
    text-decoration: none;
    color: #ffffff;
}
.rtw-referrals-row {
    display: flex;
    margin-left: -15px;
    margin-right: -15px;
    flex-wrap: wrap;
}
.rtw-referrals-column {
    width: 50%;
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 30px;
    display: flex;
}
.rtw-referrals-column-content {
    background-color: #ffffff;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    box-shadow: 2px 3px 20px rgba(0,0,0,0.22);
    border-radius: 6px;
    overflow: hidden;
}
.rtw-referrals-column-content ul li {
    position: relative;
    padding-left: 20px;
    padding-bottom: 10px;
}
.rtw-referrals-column-content ul li::before {
    content: "";
    width: 10px;
    height: 10px;
    background-color: #babdc6;
    position: absolute;
    border-radius: 50%;
    left: 0;
    top: 5px;
}
.rtw-referrals-column-content ul,.rtw-referrals-column-content ol {
    margin: 0;
    padding: 20px;
}
.rtw-referrals-column-content ol li {
    list-style-type: none;
    padding-bottom: 10px;
    padding-left: 30px;
    position: relative;
}
.rtw-referrals-column-content ol li p{
    margin: 0;
}
.rtw-key-count {
    position: absolute;
    width: 25px;
    left: -6px;
    top: 6px;
    height: 25px;
    border: 2px solid #ddd;
    border-radius: 50%;
    text-align: center;
    line-height: 18px;
    font-size: 16px;
}
/*middle content*/
/*start commission setiing tab*/
table{
    width: 100%;
}
.rtw-table, .rtwwcpig_tab_div > .form-table {
    width:  100%;
    background-color: #ffffff;
    margin-top: 20px;
}
.rtw-table th, .rtw-table td, .rtwwcpig_tab_div > .form-table td, .rtwwcpig_tab_div > .form-table th {
    border-bottom: 1px solid #e2dbdb;
    background-color: #ffffff;
    padding: 20px;
}
.rtw-table input[type=number], .rtwwcpig_tab_div > .form-table input[type=number]{
    border: 1px solid #dddddd;
    border-radius: 4px;
    padding: 10px;
    height: auto;
    width: 100px;
}
.rtw-table table th, .rtw-table table td{
    border: none;
    padding: 3px 15px 3px 0;
}
.rtw-data-table-wrapper{
    margin-top: 20px;
    background-color: #ffffff;
    box-shadow: 0 2px 28px rgba(0,0,0,.22);
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_filter input[type="search"],
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_length select{
    border: 1px solid #dddddd;
    padding: 4px 10px;
    border-radius: 4px;
    height: auto;
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_filter input[type="search"]{
    width: 150px;
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_length,
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_filter {
    margin: 10px;
}
.rtwwcpig_detailed_coupon_list.rtw-data-table-wrapper {
    width: 100%;
    padding-top: 20px;
}
.rtwwcpig_data_table{
    text-align: center;
}
.rtwwcpig_data_table tbody td{
    border-bottom: 1px solid #dddddd;
    padding: 13px 10px !important;
    font-size: 14px;
}
.rtwwcpig_data_table  thead,
.rtwwcpig_data_table tfoot{
    background-color: #90A4AE;
    color: #ffffff;
}
.rtwwcpig_data_table thead th,
.rtwwcpig_data_table tfoot th{
    border:none !important;
    padding: 15px 10px !important;
    text-align: center;
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_info,
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_paginate{
    padding: 10px;
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_paginate .paginate_button {
    background-color: #f1f1f1 !important;
    margin: 0 6px;
}
.rtw-data-table-wrapper .dataTables_wrapper .dataTables_paginate .paginate_button.current{
    background: #dddddd !important;
    border-color: #f1f1f1;
}
/*end referrals tab*/
/*custom checkbox*/
.rtwwcpig_coupons_settings td label,
.rtwwcpig-label {
    position: relative;
    padding-left: 10px;
}
.rtwwcpig_coupons_settings td label input[type="checkbox"]::before ,
.rtw-table td label input[type="checkbox"]::before{
    content: "" !important;
    border: 2px solid #ddd;
    width: 17px !important;
    height: 17px !important;
    display: inline-block !important;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 3px;
    transition: 0.3s linear all;
    margin: 0 !important;
}
.rtwwcpig_coupons_settings td label input[type="checkbox"],
.rtwwcpig_coupons_settings td label input[type="checkbox"]:focus,
.rtw-table td label input[type="checkbox"],
.rtw-table td label input[type="checkbox"]:focus{
    background-color: #ffffff;
    margin: 0;
    border: none;
    box-shadow: none;
}
.rtwwcpig_coupons_settings td label input[type="checkbox"]::after,
.rtw-table td label input[type="checkbox"]::after {
    content: "\f147";
    font-family: 'dashicons';
    position: absolute;
    left: -2px;
    font-size: 23px;
    top: 11px;
    cursor: pointer;
    transition: 0.3s linear all;
    transform: scale(0);
    opacity: 0;
    color: #3bc93b;
}
.rtwwcpig_coupons_settings td label input[type="checkbox"]:checked::after ,
.rtw-table td label input[type="checkbox"]:checked::after{
 transition: 0.3s linear all;
 transform: scale(1);
 opacity: 1;
}
.rtw-custom-radio {
    position: relative;
    display: inline-block;
    padding-left: 10px;
    margin-right: 10px;
    vertical-align: middle;
}
.rtw-custom-radio input[type="radio"] ~ label::before {
    content: "";
    position: absolute;
    left: -2px;
    top: 0;
    width: 17px;
    height: 17px;
    border: 2px solid #dddddd;
    border-radius: 50%;
}
.rtw-custom-radio input[type="radio"]{
    opacity: 0;
    margin: 0;
    width: auto;
    height: auto;
}
.rtw-custom-radio input[type="radio"] ~ label::after {
    content: "";
    width: 8px;
    height: 8px;
    background-color: #9575CD;
    display: inline-block;
    border-radius: 50%;
    position: absolute;
    left: 5px;
    opacity: 0;
    transition: 0.3s linear all;
    top: 7px;
    transform: scale(0);
}
.rtw-custom-radio input[type="radio"]:checked ~ label::after {
    transition: 0.3s linear all;
    opacity: 1;
    transform: scale(1);
}
select.rtwwcpig_select {
    width: 230px;
    height: auto;
    padding: 8px 10px;
    border-radius: 4px;
}
/*end custom checkbox*/
/*footer*/
.rtw-footer {
    background-color: #dddddd;
    padding: 20px;
    text-align: center;
    margin-top: 30px;
}
.rtwwqcp-help-wrapper {
    display: flex;
    flex-wrap: wrap;
 }
 .rtwwqcp-help-image {
    width: 100px;
    margin: 0 auto 40px;
 }
 .rtwwqcp-help-image img {
    max-width: 100%;
    width: auto;
 }
.rtwwqcp-column {
    flex: 0 0 50%;
    padding-left: 1%;
    padding-right: 1%;
    text-align: center;
    margin: 20px 0;
}
 .rtwwdcp-inner-content {
    padding: 40px 20px;
    width: 100%;
    box-shadow: 0 3px 3px -2px rgba(0,0,0,.2),0 3px 4px 0 rgba(0,0,0,.14),0 1px 8px 0 rgba(0,0,0,.12);
 }
 .rtwwqcp-column.rtwwqcp-faq-column {
    flex: 0 0 100%;
 }
 .rtwwqcp-faq-wrapper {
    text-align: left;
    font-size: 15px;
 }
 .rtwwqcp-faq-heading {
    font-size: 18px;
    margin: 0 0 20px 0;
    font-weight: bold;
    cursor: pointer;
    background-color: #f4f4f4;
    padding: 15px;
 }
 .rtwwqcp-faq-heading::before {
    content: "\f347";
    font-family: 'dashicons';
    vertical-align: middle;
    margin-right: 10px;
 }
 .rtwwqcp-faq-heading.active::before {
    content: "\f343";
 }
 .rtwwqcp-faq-desc {
    display: none;
    padding-bottom: 20px;
    padding-left: 42px;
    font-size: 16px;
    line-height: 1.7;
 }
