language: php

sudo: false

php:
  - '5.6'
  - '7.0'
  - '7.1'
  - nightly

matrix:
  fast_finish: true
  include:
    - php: '5.6'
      env: COMPOSER_FLAGS="--prefer-lowest"
  allow_failures:
    - php: nightly

cache:
  directories:
    - $HOME/.composer/cache/files

before_install:
  - |
    if [ "$TRAVIS_PHP_VERSION" = "nightly" ]; then
      COMPOSER_FLAGS="$COMPOSER_FLAGS --ignore-platform-reqs"
    fi;

install:
  - composer update --no-interaction --no-progress --no-suggest --prefer-dist $COMPOSER_FLAGS
  - wget https://github.com/satooshi/php-coveralls/releases/download/v1.0.0/coveralls.phar

before_script:
  - mkdir -p build/logs

script:
  - vendor/bin/phpunit --coverage-clover build/logs/clover.xml

after_script:
  - php coveralls.phar -v
