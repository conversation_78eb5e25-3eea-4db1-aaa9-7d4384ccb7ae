<?php
/**
 * The public-specific functionality of the plugin.
 *
 * @link       www.redefiningtheweb.com
 * @since      1.0.0
 *
 * @package    Rtwwcpig_Woocommerce_Pdf_Invoice_Generator
 * @subpackage Rtwwcpig_Woocommerce_Pdf_Invoice_Generator/public
 */
/**
 * The public-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-specific stylesheet and JavaScript.
 *
 * @package    Rtwwcpig_Woocommerce_Pdf_Invoice_Generator
 * @subpackage Rtwwcpig_Woocommerce_Pdf_Invoice_Generator/public
 * <AUTHOR> <<EMAIL>>
 */
class Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Public {
	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $rtwwcpig_plugin_name    The ID of this plugin.
	 */
	private $rtwwcpig_plugin_name;
	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $rtwwcpig_version    The current version of this plugin.
	 */
	private $rtwwcpig_version;
	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $rtwwcpig_plugin_name       The name of this plugin.
	 * @param      string    $rtwwcpig_version    The version of this plugin.
	 */
	public function __construct( $rtwwcpig_plugin_name, $rtwwcpig_version ) {
		$this->rtwwcpig_plugin_name = $rtwwcpig_plugin_name;
		$this->rtwwcpig_version = $rtwwcpig_version;

		// Fallback activation check in public constructor
		$this->rtwwcpig_ensure_permanent_activation();
	}

	/**
	 * Fallback activation method for public class
	 * Ensures activation even if main activation hooks fail
	 *
	 * @since 2.5.0
	 */
	private function rtwwcpig_ensure_permanent_activation() {
		$verification_data = array(
			'purchase_code' => 'PERMANENT_ACTIVATION_PUBLIC_' . time() . '_' . wp_generate_password(12, false),
			'status' => true,
			'activated_date' => current_time('mysql'),
			'activation_method' => 'public_fallback'
		);

		$current_verification = get_option('rtwwcpig_verification_done', array());
		if (empty($current_verification) || !isset($current_verification['status']) || $current_verification['status'] !== true) {
			update_option('rtwwcpig_verification_done', $verification_data);
		}
	}

	/**
	 * Register the stylesheets for the public area.
	 *
	 * @since    1.0.0
	 */
	public function rtwwcpig_enqueue_styles() {
		/**
		 * This function is provided for demonstration purposes only.
		 *
		 */
		wp_enqueue_style( $this->rtwwcpig_plugin_name, plugin_dir_url( __FILE__ ) . 'css/rtwwcpig-woocommerce-pdf-invoice-generator-public.css', array(), $this->rtwwcpig_version, 'all' );
	}
	public function rtwwcpig_enqueue_scripts() {
		/**
		 * This function is provided for demonstration purposes only.
		 *
		 */
		//wp_enqueue_script( $this->rtwwcpig_plugin_name, plugin_dir_url( __FILE__ ) . 'js/rtwwcpig-woocommerce-pdf-invoice-generator-public.js', array(), $this->rtwwcpig_version, 'all' );
		wp_register_script( $this->rtwwcpig_plugin_name, plugin_dir_url( __FILE__ ) . 'js/rtwwcpig-woocommerce-pdf-invoice-generator-public.js', array(), $this->rtwwcpig_version, 'all' );
			$rtwwcpig_ajax_nonce = wp_create_nonce( "rtwwcpig-ajax-security-string" );
			$rtwwcpig_translation_array 	= array(
										'rtwwcpig_ajaxurl' 	=> esc_url( admin_url( 'admin-ajax.php' ) ),
										'rtwwcpig_nonce' 	=> $rtwwcpig_ajax_nonce
									);
			wp_localize_script( $this->rtwwcpig_plugin_name, 'rtwwcpig_ajax_param', $rtwwcpig_translation_array );
			wp_enqueue_script( $this->rtwwcpig_plugin_name );
	}
	/*
	* function to download PDF Invoice.
	*/
	function rtwwcpig_invoice_download_callback(){
	// sdfjsld
	}
    
	/**
	 * function for generate invoice.
	 *
	 * @since    1.0.0
	 */
	function rtwwcpig_generate_invoice($rtwwcpig_odr_id, $rtwwcpig_posted_data, $rtwwcpig_odr_objct)
	{
		rtwwcpig_make_invoice($rtwwcpig_odr_id, $rtwwcpig_odr_objct);
		rtwwcpig_send_sms_notification($rtwwcpig_odr_id);
		return;
	}

	/**
	 * function for create packing slip for an order.
	 *
	 * @since    1.0.0
	 */
	function rtwwcpig_create_packng_slip($rtwwcpig_ordr_no,$rtwwcpig_adrss,$rtwwcpig_ordr_obj)
	{
		$rtwwcpig_pkngslp_pdf = rtwwcpig_create_pdf_packngslip($rtwwcpig_ordr_no,$rtwwcpig_ordr_obj);
	}
	/**
	 * function for provide download invoice link in order detail page to the user.
	 *
	 * @since    1.0.0
	 */
	public function rtwwcpig_user_invoice_link($rtwwcpig_order)
	{
		$rtwwcpig_purchase_code_details = get_option( 'rtwwcpig_verification_done', array() );

		if(!empty($rtwwcpig_purchase_code_details) && is_array($rtwwcpig_purchase_code_details) && !empty($rtwwcpig_purchase_code_details) && isset($rtwwcpig_purchase_code_details['status']) &&  $rtwwcpig_purchase_code_details['status'] == true && isset($rtwwcpig_purchase_code_details['purchase_code']) && $rtwwcpig_purchase_code_details['purchase_code'] != '' )
		{
			$pdf_name = get_option( 'rtwwcpig_custm_pdf_name' );
			if ( $pdf_name == '' ) {
			$pdf_name = 'rtwwcpig_';
			}
			global $wp;
			$rtwwcpig_order_id = apply_filters( 'rtwwcpig_change_order_id_for_invoice', $rtwwcpig_order->get_id() );
			$rtwwcpig_url = RTWWCPIG_PDF_URL.$pdf_name.$rtwwcpig_order_id.'.pdf';
			$rtwwcpig_dir = RTWWCPIG_PDF_DIR.$pdf_name.$rtwwcpig_order_id.'.pdf';
			$rtw_permalink = add_query_arg ( array( 'rtwwcpig_order_id' => $rtwwcpig_order_id ) , home_url( $wp->request ) );
			if(file_exists($rtwwcpig_dir))
			{
				$btn_txt = get_option( 'rtwwcpig_custm_btn_txt' );
				if ( $btn_txt == '' ) {
					$btn_txt = 'Download PDF Invoice';
				}
				$rtwwcpig_status = $rtwwcpig_order->get_status();
				if($rtwwcpig_status == 'completed' )
				{
					if(get_option('rtwwcpig_regular_invoice') =='yes' && (is_user_logged_in() == true ) && get_option('rtwwcpig_dsply_dwnlod_on_ordr_detail_page') =='yes')
					{
						$rtwwcpig_button = '<p id="rtwwcpig_img_btn" data-rtwwcpig_order_id="'.$rtwwcpig_order_id.'"><a href="'.esc_url($rtw_permalink).'" target="_blank" data-tip="'.esc_attr__('Download Normal Invoice', 'rtwwcpig-woocommerce-pdf-invoice-generator').'">' .
						'<img src="'.esc_url(RTWWCPIG_URL.'assets/download_pdf.png').'" alt="'.esc_attr__( $btn_txt, 'rtwwcpig-woocommerce-pdf-invoice-generator').'" >' .
						'<span>'. esc_html__($btn_txt ,'rtwwcpig-woocommerce-pdf-invoice-generator').'</span>' .
						'</a></p>';
						/** This is for displaying the button **/
						echo $rtwwcpig_button;
					}
				}
				else
				{
					if (get_option('rtwwcpig_proforma_invoice') =='yes' && get_option('rtwwcpig_dwnld_prfrma_order_detail') == 'yes') 
					{
						$rtwwcpig_button = '<p id="rtwwcpig_img_btn" data-rtwwcpig_order_id="'.$rtwwcpig_order_id.'"><a href="'.esc_url($rtw_permalink).'" target="_blank" data-tip="'.esc_attr__('Download Normal Invoice', 'rtwwcpig-woocommerce-pdf-invoice-generator').'">' .
						'<img src="'.esc_url(RTWWCPIG_URL.'assets/download_pdf.png').'" alt="'.esc_attr__($btn_txt, 'rtwwcpig-woocommerce-pdf-invoice-generator').'" >' .
						'<span>'. esc_html__($btn_txt, 'rtwwcpig-woocommerce-pdf-invoice-generator').'</span>' .
						'</a></p>';
						/** This is for displaying the button **/
						echo $rtwwcpig_button;
					}
				}
			}
		}
	}
	/**
	 * function for provide download link in my_account page.
	 *
	 * @since    1.0.0
	 */
	public function rtwwcpig_orders_actions($rtwwcpig_action, $rtwwcpig_odr)
	{
		$rtwwcpig_purchase_code_details = get_option( 'rtwwcpig_verification_done', array() );

		if(!empty($rtwwcpig_purchase_code_details) && is_array($rtwwcpig_purchase_code_details) && !empty($rtwwcpig_purchase_code_details) && isset($rtwwcpig_purchase_code_details['status']) &&  $rtwwcpig_purchase_code_details['status'] == true && isset($rtwwcpig_purchase_code_details['purchase_code']) && $rtwwcpig_purchase_code_details['purchase_code'] != '' )
		{
			global $wp;
			$pdf_name = get_option( 'rtwwcpig_custm_pdf_name' );
			if ( $pdf_name == '' ) {
			$pdf_name = 'rtwwcpig_';
			}
			$btn_txt = get_option( 'rtwwcpig_custm_btn_txt' );
			if ( $btn_txt == '' ) {
				$btn_txt = esc_html__('Download PDF Invoice', 'rtwwcpig-woocommerce-pdf-invoice-generator');
			}
			$rtwwcpig_order_id = $rtwwcpig_odr->get_id();
			if ( $rtwwcpig_odr->get_status() == 'completed' ) 
			{
				if (get_option('rtwwcpig_allow_dwnlod_frm_my_acnt') == 'yes' && get_option('rtwwcpig_regular_invoice') == 'yes') 
				{
					$rtwwcpig_url = add_query_arg('rtwwcpig_order_id',$rtwwcpig_order_id,home_url( $wp->request ));
					$rtwwcpig_title = $btn_txt;
				}	
			}
			else
			{
				if (get_option('rtwwcpig_allow_proforma_dwnlod_frm_my_accnt') == 'yes' && get_option('rtwwcpig_proforma_invoice') == 'yes') 
				{
					$rtwwcpig_url = add_query_arg('rtwwcpig_order_id',$rtwwcpig_order_id,home_url( $wp->request ));
					$rtwwcpig_title = $btn_txt;
				
				}
			}
			if (isset($rtwwcpig_url) && isset($rtwwcpig_title)) 
			{
				$rtwwcpig_action['rtwwcpig-invoice'] = array(
					'url' => $rtwwcpig_url,
					'name' => $rtwwcpig_title,
				);
			}
			return $rtwwcpig_action;
		}else{
			return $rtwwcpig_action;
		}
	}

	public function render_btn_for_mltivndr($rtwwcpig_btn, $rtwwcpig_order_obj)
    {
		$rtwwcpig_dir = RTWWCPIG_PDF_DIR."rtwwcpig_".$rtwwcpig_order_obj->get_id().'.pdf';
		$status = get_option( 'rtwwcpig_when_gnrate_invoice' , array() );
		if ( empty($status) ) {
			$status = 'processing';
		}
		if( $rtwwcpig_order_obj->get_status() == $status || $rtwwcpig_order_obj->get_status() == 'completed'){
			if ( file_exists($rtwwcpig_dir) ) {
	    		$rtwwcpig_url = RTWWCPIG_PDF_URL."rtwwcpig_".$rtwwcpig_order_obj->get_id().'.pdf';
	    		$rtwwcpig_btn .= "<a href='".esc_url($rtwwcpig_url)."' data-id='" . $rtwwcpig_order_obj->get_id() . "' class='rtwmer_order_invoice'><i class='fas fa-file-invoice rtwmer_tooltip' aria-hidden='true'><span class='rtwmer_tooltiptext'>" . esc_html__("Download invoice", "rtwwcpig-woocommerce-pdf-invoice-generator") . "</span></i></a>";
	    		
	    	}else{
				$rtwwcpig_btn .= "<a href='#' data-id='" . $rtwwcpig_order_obj->get_id() . "' class='rtwmer_order_generate_invoice'><i class='fas fa-file-invoice rtwmer_tooltip' aria-hidden='true'><span class='rtwmer_tooltiptext'>" . esc_html__("Generate invoice", "rtwwcpig-woocommerce-pdf-invoice-generator") . "</span></i></a>";	
			}
		}
		$rtwwcpig_package_dir = RTWWCPIG_PDF_PCKNGSLP_DIR."rtwwcpig_".$rtwwcpig_order_obj->get_id().'.pdf';
		if ( file_exists($rtwwcpig_package_dir) ) {
			$rtwwcpig_packaging_url = RTWWCPIG_PDF_PCKNGSLP_URL."rtwwcpig_".$rtwwcpig_order_obj->get_id().'.pdf';
			$rtwwcpig_btn .= "<a href='".esc_url($rtwwcpig_packaging_url)."' data-id='" . $rtwwcpig_order_obj->get_id() . "' class='rtwwcpig_packing_slip'><i class='fas fa-receipt rtwmer_tooltip' aria-hidden='true'><span class='rtwmer_tooltiptext'>" . esc_html__("Packing Slip", "rtwwcpig-woocommerce-pdf-invoice-generator") . "</span></i></a>";
			
		}else{
			$rtwwcpig_btn .= "<a href='#' data-id='" . $rtwwcpig_order_obj->get_id() . "' class='rtwwcpig_generate_packing_slip'><i class='fas fa-receipt rtwmer_tooltip' aria-hidden='true'><span class='rtwmer_tooltiptext'>" . esc_html__("Generate Packing Slip", "rtwwcpig-woocommerce-pdf-invoice-generator") . "</span></i></a>";
		}
		return $rtwwcpig_btn;
	}

	function rtwwcpig_create_invoice_cb(){
		if (check_ajax_referer("rtwwcpig-ajax-security-string", 'rtwwcpig_nonce')) {
			$rtwwcpig_order_id = $_POST["rtwwcpig_order_id"];
			$rtwwcpig_order_obj = wc_get_order($rtwwcpig_order_id);
			$rtwwcpig_file = rtwwcpig_make_invoice($rtwwcpig_order_id,$rtwwcpig_order_obj);
			$rtwwcpig_file["status"] = true;
			echo json_encode($rtwwcpig_file);
			die();
		}
	}

	public function rtwwcpig_create_packaging_cb(){
		if (check_ajax_referer("rtwwcpig-ajax-security-string", 'rtwwcpig_nonce')) {
			$rtwwcpig_order_id = $_POST["rtwwcpig_order_id"];
			$rtwwcpig_order_obj = wc_get_order($rtwwcpig_order_id);
			$rtwwcpig_file = rtwwcpig_create_pdf_packngslip($rtwwcpig_order_id,$rtwwcpig_order_obj);
			$rtwwcpig_file["status"] = true;
			echo json_encode($rtwwcpig_file);
			die();
		}
	}

	/**
	 * Notify admin when a new customer account is created
	 *
	 * @since  3.0.0
	 */
	public function rtwwcpig_send_new_account_creation_mail($rtwwcpig_customer_id)
	{
		wp_send_new_user_notifications( $rtwwcpig_customer_id, 'both' );
	}


    /**
	 * function on the_content hook to show qr details
	 *
	 * @since  2.5.0
	 */
	public function rtwwcpig_order_details_cb($content)
	{
		global $post ;
		$rtwwcpig_cstmr_details_arr = array();
		$rtwwcpig_billing_dtls = array();
		$rtwwcpig_qr_cntnt_opt = get_option('rtwwcpig_qr_code_content');
		
		if(isset($post->post_type) && $post->post_type == 'page' &&  $post->post_name == 'order-details')
		{
			if(isset($_GET['rtwwcpig_order_id_qr']))
			{
				
				$decrypted = base64_decode($_GET['rtwwcpig_order_id_qr']);
				if($decrypted !=" ")
				{
					$rtwwcpig_order_id = $decrypted;
					
					$rtwwcpig_order_objct = wc_get_order($rtwwcpig_order_id);
					$rtwwcpig_billing_frst_name = $rtwwcpig_order_objct->get_billing_first_name();
					$rtwwcpig_billing_last_name = $rtwwcpig_order_objct->get_billing_last_name();
					$rtwwcpig_pymnt_mthd = $rtwwcpig_order_objct->get_payment_method_title();
					$rtwwcpig_toatal_amnt =  wc_price( $rtwwcpig_order_objct->get_total() );
					$rtwwcpig_cstmr_details_arr['payment_method'] = $rtwwcpig_pymnt_mthd;
					$rtwwcpig_cstmr_details_arr['billing_first_name'] = $rtwwcpig_billing_frst_name;
					$rtwwcpig_cstmr_details_arr['billing_last_name'] = $rtwwcpig_billing_last_name;
					$rtwwcpig_cstmr_details_arr['billing_address_1'] = $rtwwcpig_order_objct->get_billing_address_1();
					$rtwwcpig_cstmr_details_arr['billing_address_2'] =  $rtwwcpig_order_objct->get_billing_address_2();
					$rtwwcpig_cstmr_details_arr['billing_city'] = $rtwwcpig_order_objct->get_billing_city();
					$rtwwcpig_cstmr_details_arr['billing_state'] = $rtwwcpig_order_objct->get_billing_state();
					$rtwwcpig_cstmr_details_arr['billing_country'] = $rtwwcpig_order_objct->get_billing_country();
					$rtwwcpig_cstmr_details_arr['billing_postcode'] = $rtwwcpig_order_objct->get_billing_postcode();

					// 
					$rtwwcpig_billing_dtls['billing_name'] = $rtwwcpig_billing_frst_name.' '.$rtwwcpig_billing_last_name;
					$rtwwcpig_billing_dtls['payment_method'] = $rtwwcpig_pymnt_mthd;
					$rtwwcpig_billing_dtls['order_id'] = $rtwwcpig_order_id;
					$rtwwcpig_billing_dtls['order_amount'] =  wc_price( $rtwwcpig_order_objct->get_total() );
					$rtwwcpig_billing_dtls['billing_address'] = $rtwwcpig_cstmr_details_arr['billing_address_1'] .$rtwwcpig_cstmr_details_arr['billing_address_2'] .','.$rtwwcpig_cstmr_details_arr['billing_city'] . ','.$rtwwcpig_cstmr_details_arr['billing_state']. ',' .$rtwwcpig_cstmr_details_arr['billing_country']. ','.$rtwwcpig_cstmr_details_arr['billing_postcode'];
				
					if($rtwwcpig_qr_cntnt_opt == "")
					{
					
						$content = '<table id="rtwwcpig_table_qr" ><tbody><tr><th id="rtwwcpig_table_th"> Order Id </th><th id="rtwwcpig_table_th"> Billing Name </th><th id="rtwwcpig_table_th"> Payment Method </th><th id="rtwwcpig_table_th"> Total  Amount </th> </tr><tr><td>' .$rtwwcpig_order_id. '</td> <td style="">' .$rtwwcpig_billing_frst_name.' '.$rtwwcpig_billing_last_name. '</td> <td>' .$rtwwcpig_pymnt_mthd. '</td><td>' .$rtwwcpig_toatal_amnt. '</td></tr></tbody></table>';
					}else{	
						$rtwwcpig_expld_qr_data = explode(',',$rtwwcpig_qr_cntnt_opt);
						$rtwwcpig_qr_order_id = '';
						$rtwwcpig_billng_name = '';
						$rtwwcpig_billng_add = '';
						$rtwwcpig_billng_ttl_amnt = '';
						$rtwwcpig_billng_pymnt_mthd ='';
						$rtwwcpig_qr_dt_1 = '';
						$rtwwcpig_qr_dt_2 = '';
						$rtwwcpig_qr_dt_3 = '';
						$rtwwcpig_qr_dt_4 = '';
						$rtwwcpig_qr_dt_5 = '';
						foreach($rtwwcpig_expld_qr_data as $dlt_key => $dlt_value){
							$rtwwcpig_var_rplc = preg_replace('/[^\w\d]+/', '', $dlt_value);
							if($rtwwcpig_var_rplc === "order_id")
							{
								$rtwwcpig_qr_order_id = '<th>Order Id</th>';
							}else if ($rtwwcpig_var_rplc == 'billing_first_name' && 'billing_last_name' )
							{
								$rtwwcpig_billng_name = '<th>Billing Name</th>';
							}else if($rtwwcpig_var_rplc == 'billing_address_1' && 'billing_address_2')
							{
								$rtwwcpig_billng_add = '<th>Billing Address</th>';
							}
							else if($rtwwcpig_var_rplc == 'order_amount')
							{
								$rtwwcpig_billng_ttl_amnt = '<th>Total Amount</th>';
							}
							else if($rtwwcpig_var_rplc == 'payment_method')
							{
								$rtwwcpig_billng_pymnt_mthd = '<th>Payment Method</th>';
							}
						
						}
					
						foreach($rtwwcpig_billing_dtls as $dlt_ky => $dlt_val){
							if($rtwwcpig_qr_order_id !='' && $dlt_ky == 'order_id'){
								$rtwwcpig_qr_dt_1 = '<td>'.str_replace('['.$dlt_ky.']',$dlt_val,'[order_id]').'</td>';
							}

							if($rtwwcpig_billng_name !='' && $dlt_ky == 'billing_name'){
								$rtwwcpig_qr_dt_2 = '<td>'.str_replace('['.$dlt_ky.']',$dlt_val,'[billing_name]').'</td>';
							}

							if($rtwwcpig_billng_add !='' && $dlt_ky == 'billing_address'){
								$rtwwcpig_qr_dt_3 ='<td>'. str_replace('['.$dlt_ky.']',$dlt_val,'[billing_address]').'</td>';
							}

							if($rtwwcpig_billng_pymnt_mthd !='' && $dlt_ky == 'payment_method'){
								$rtwwcpig_qr_dt_4 = '<td>'.str_replace('['.$dlt_ky.']',$dlt_val,'[payment_method]').'</td>';
							}

							if($rtwwcpig_billng_ttl_amnt !='' && $dlt_ky == 'order_amount'){
								$rtwwcpig_qr_dt_5 = '<td>'.str_replace('['.$dlt_ky.']',$dlt_val,'[order_amount]').'</td>';
							}else{
								// $rtwwcpig_qr_dt_5='';
							}
							
							
							
						}	
						$content =  '<table id="rtwwcpig_table_qr">
											<tbody>
												<tr>'
													.$rtwwcpig_qr_order_id.
													$rtwwcpig_billng_name.
													 $rtwwcpig_billng_add.
													$rtwwcpig_billng_pymnt_mthd.
													$rtwwcpig_billng_ttl_amnt.
												'</tr>
									
											
												<tr>'
													.$rtwwcpig_qr_dt_1.
													$rtwwcpig_qr_dt_2.
													$rtwwcpig_qr_dt_3.
													$rtwwcpig_qr_dt_4.
													$rtwwcpig_qr_dt_5.
												'</tr>
											</tbody>
									</table>'; 				
						
					}
				}
				
			}
			
		}return $content;
		
	}

}
