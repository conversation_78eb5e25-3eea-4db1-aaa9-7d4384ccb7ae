language: php

dist: xenial

group: edge

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - 7.3
  - 7.4snapshot
  - nightly

matrix:

  include:

    - env: LINT=1
      php: 7.1
    #- env: COVERAGE=1
    #  php: 7.1

  allow_failures:
    - php: nightly

install:
  - composer self-update
  - echo "${TRAVIS_PHP_VERSION:0:3}"
  - |
    if [[ "${TRAVIS_PHP_VERSION:0:7}" == "nightly" ]]; then
      composer install --ignore-platform-reqs
    elif [[ "${TRAVIS_PHP_VERSION:0:11}" == "7.4snapshot" ]]; then
      composer install --ignore-platform-reqs
    else
      composer install
    fi

script:
  - |
    if [[ "$LINT" == "1" ]]; then
      composer cs
    elif [[ "$COVERAGE" == "1" ]]; then
      composer coverage
    else
      composer test
    fi

notifications:
  email:
    on_success: change
    on_failure: always
