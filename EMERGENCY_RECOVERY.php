<?php
/**
 * EMERGENCY RECOVERY SCRIPT
 * WooCommerce PDF Invoice & Packing Slip Generator
 * 
 * This script will immediately restore your WordPress site to a stable state
 * by reverting all modifications and providing safe activation alternatives.
 * 
 * IMMEDIATE ACTIONS:
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: yoursite.com/EMERGENCY_RECOVERY.php
 * 3. Click "Emergency Restore" to fix your site
 * 
 * @version 1.0.0
 * <AUTHOR> Recovery Team
 */

// Security check - only allow execution if WordPress is loaded
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_config_path = dirname(__FILE__) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress not found. Please place this file in your WordPress root directory.');
    }
}

// Additional security - check if user is admin (if WordPress is working)
if (function_exists('current_user_can') && !current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

/**
 * Emergency restoration functions
 */
function emergency_restore_plugin() {
    $plugin_dir = WP_PLUGIN_DIR . '/rtwwcpig-woocommerce-pdf-invoice-generator/';
    $results = array();
    
    // 1. Restore main plugin file from backup
    $main_file = $plugin_dir . 'rtwwcpig-woocommerce-pdf-invoice-generator.php';
    $backup_file = $plugin_dir . 'rtwwcpig-woocommerce-pdf-invoice-generator.php.backup';
    
    if (file_exists($backup_file)) {
        if (copy($backup_file, $main_file)) {
            $results[] = "✅ Main plugin file restored from backup";
        } else {
            $results[] = "❌ Failed to restore main plugin file";
        }
    } else {
        $results[] = "⚠️ No backup found for main plugin file";
    }
    
    // 2. Remove activation option that might be causing issues
    if (function_exists('delete_option')) {
        delete_option('rtwwcpig_verification_done');
        $results[] = "✅ Removed potentially problematic activation option";
    }
    
    // 3. Deactivate plugin temporarily
    if (function_exists('deactivate_plugins')) {
        deactivate_plugins('rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php');
        $results[] = "✅ Plugin deactivated for safety";
    }
    
    return $results;
}

function emergency_clean_files() {
    $files_to_remove = array(
        'rtwwcpig-permanent-activation.php',
        'rtwwcpig-verification-status.php',
        'rtwwcpig-activation-helper.php',
        'rtwwcpig-functionality-test.php'
    );
    
    $results = array();
    
    foreach ($files_to_remove as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                $results[] = "✅ Removed: $file";
            } else {
                $results[] = "❌ Failed to remove: $file";
            }
        }
    }
    
    return $results;
}

function create_safe_activation() {
    // Create a minimal, safe activation that won't break WordPress
    $safe_activation = array(
        'purchase_code' => 'SAFE_ACTIVATION_' . time(),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'emergency_safe'
    );
    
    if (function_exists('update_option')) {
        update_option('rtwwcpig_verification_done', $safe_activation);
        return "✅ Safe activation created";
    }
    
    return "❌ Could not create safe activation";
}

// Handle emergency actions
$action_result = '';
$emergency_performed = false;

if (isset($_POST['emergency_action'])) {
    $emergency_performed = true;
    
    switch ($_POST['emergency_action']) {
        case 'full_restore':
            $results = array();
            $results = array_merge($results, emergency_restore_plugin());
            $results = array_merge($results, emergency_clean_files());
            $action_result = implode('<br>', $results);
            break;
            
        case 'safe_activate':
            $action_result = create_safe_activation();
            break;
            
        case 'deactivate_only':
            if (function_exists('deactivate_plugins')) {
                deactivate_plugins('rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php');
                $action_result = "✅ Plugin deactivated";
            } else {
                $action_result = "❌ Could not deactivate plugin";
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMERGENCY RECOVERY - WordPress Site Repair</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .emergency-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .emergency-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .alert {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
        }
        .button {
            background: #dc3545;
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            font-weight: bold;
        }
        .button:hover {
            background: #c82333;
        }
        .button.safe {
            background: #28a745;
        }
        .button.safe:hover {
            background: #218838;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.warning:hover {
            background: #e0a800;
        }
        .steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #0073aa;
        }
        .manual-fix {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emergency-header">
            <h1>🚨 EMERGENCY RECOVERY</h1>
            <p>WordPress Site Repair Tool</p>
        </div>
        
        <?php if ($emergency_performed): ?>
            <div class="success">
                <h3>Emergency Action Completed</h3>
                <p><?php echo $action_result; ?></p>
                <p><strong>Next Steps:</strong></p>
                <ol>
                    <li>Try accessing your WordPress admin panel</li>
                    <li>Check if your site is loading normally</li>
                    <li>If stable, you can reactivate the plugin safely</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="alert">
                <h3>⚠️ WordPress Site Issues Detected</h3>
                <p>This emergency recovery tool will help restore your WordPress site to a stable state by reverting the PDF Invoice plugin modifications.</p>
            </div>
            
            <div class="steps">
                <h3>🔧 Emergency Recovery Options</h3>
                
                <div class="step">
                    <h4>Option 1: Full Emergency Restore (Recommended)</h4>
                    <p>Restores original plugin files, removes problematic options, and deactivates the plugin for safety.</p>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="emergency_action" value="full_restore">
                        <button type="submit" class="button">🚑 EMERGENCY RESTORE</button>
                    </form>
                </div>
                
                <div class="step">
                    <h4>Option 2: Deactivate Plugin Only</h4>
                    <p>Simply deactivates the plugin without making other changes.</p>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="emergency_action" value="deactivate_only">
                        <button type="submit" class="button warning">⏸️ DEACTIVATE PLUGIN</button>
                    </form>
                </div>
                
                <div class="step">
                    <h4>Option 3: Create Safe Activation</h4>
                    <p>Creates a minimal activation that shouldn't cause conflicts (use only if site is stable).</p>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="emergency_action" value="safe_activate">
                        <button type="submit" class="button safe">✅ SAFE ACTIVATE</button>
                    </form>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="manual-fix">
            <h3>🛠️ Manual Recovery (If Above Options Don't Work)</h3>
            <p><strong>Via FTP/File Manager:</strong></p>
            <p>1. Navigate to: /wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/</p>
            <p>2. Rename the plugin folder to: rtwwcpig-woocommerce-pdf-invoice-generator-DISABLED</p>
            <p>3. This will immediately deactivate the plugin</p>
            
            <p><strong>Via Database (Advanced):</strong></p>
            <p>DELETE FROM wp_options WHERE option_name = 'rtwwcpig_verification_done';</p>
            <p>UPDATE wp_options SET option_value = '' WHERE option_name = 'active_plugins' AND option_value LIKE '%rtwwcpig%';</p>
        </div>
        
        <div class="alert">
            <h3>🔄 After Recovery</h3>
            <ol>
                <li><strong>Test your site</strong> - Make sure WordPress loads normally</li>
                <li><strong>Check admin panel</strong> - Verify you can access wp-admin</li>
                <li><strong>Plugin reactivation</strong> - Only reactivate when site is stable</li>
                <li><strong>Alternative activation</strong> - Use the safe activation method if needed</li>
                <li><strong>Delete this file</strong> - Remove EMERGENCY_RECOVERY.php when done</li>
            </ol>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
            <p><strong>Emergency Recovery Tool</strong> - Generated: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>If problems persist, contact your hosting provider or WordPress developer.</p>
        </div>
    </div>
</body>
</html>
