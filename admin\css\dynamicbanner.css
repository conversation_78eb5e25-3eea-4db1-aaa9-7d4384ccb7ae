body{
    margin: 0;
}
*,::before,::after{
    box-sizing: border-box;
}
.rtw_popup{
    background: rgba(0,0,0,0.4);
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    overflow: auto;
}
.rtw_close_icon{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    position: relative;
    border:2px solid white;
}
.rtw_close_icon:before,.rtw_close_icon:after {
    content: '';
    position: absolute;
    height: 18px;
    width: 2px;
    top: 3px;
    background: white;
    transform: rotate(
45deg
);
}
.rtw_close_icon:after {
    transform: rotate(-45deg);
}
.rtw_card{
   position: relative;
   height: 400px;
    width: 560px;
    background: linear-gradient(45deg, #211e1eeb, #d31b11d9),url(./img7.jpeg);
  
    background-size: 100% 100%;
    background-repeat: no-repeat;

    border-radius: 20px;
   
    box-shadow: 0 0 20px 8px #796868;
    font-family: sans-serif;
  
}
.rtw_card_body {
    color: white;
    align-items: center;
    padding: 2rem;
    width: 100%;
    height: 100%;
    text-align: center;
}
.rtw_card_body h2{
    font-size: 3rem;
    color: white;
    background: black;
    padding: 10px;
    text-transform: uppercase;
    text-align: center;
    transform: skew( 
-9deg
 , 
-7deg
 );
    margin-top: 4rem;
}
button {
    border: none;
    background: white;
    padding: 15px 40px;
    border-radius: 35px;
    font-weight: bold;
    box-shadow: 0px 0px 8px #f9f6f69c;
    text-transform: uppercase;
    cursor: pointer;
    position: relative;
    z-index: 1;
}
button::before{
    content: '';
    position: absolute;
    width: 0%;
    height: 100%;
    background:linear-gradient(45deg, #d31b11, #0d0d0f);
    left: 0;
    transition: all 0.3s ease-in-out;
    top: 0;
    border-radius: 35px;
    z-index: -1;
    opacity: 0;
}
button:hover{
    color: white;
}
button:hover::before{
opacity: 1;
width: 100%;
}
.rtw_card_label {
    background: white;
    padding: 10px 45px;
    text-transform: uppercase;
    font-size: 13px;
    position: absolute;
    top: 35px;
    left: -12px;
}
.rtw_card_label label{
    margin: 0;
}
.rtw_card_label::before {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    top: -12px;
    left: 0px;
    border-bottom: 12px solid black;
    border-left: 12px solid transparent;
}
.rtw_card_label::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    right: -14px;
    top: 0;
    border-top: 18px solid transparent;
    border-bottom: 18px solid transparent;
    border-left: 14px solid #ffffff;
}
.rtw_close_popup {
    position: absolute;
    top: 18px;
    right: 18px;
    font-size: 30px;
    color: white;
    cursor: pointer;
}
.price {
    background: #ffc107;
    width: auto;
    padding: 10px;
    transform: skew( 
-9deg
 , 
-7deg
 );
    width: 170px;
    position: absolute;
    right: 15px;
    bottom: 135px;
    font-size: 24px;
    font-weight: bold;
}
.price span:nth-child(2) {
    margin-left: 5px;
    color: black;
    font-size: 35px;
}
.bottom_text {
    font-size: 20px;
    font-style: italic;
    margin: 0;
    font-weight: bold;
    text-align: right;
    margin-top: 2rem;
}
@media(max-width:576px){
    .rtw_card{
        width: 85%;
    }
    .rtw_card_body h2 {
        font-size: 2rem;
        text-align: center;
    }
    button{
        margin-top: 5rem;
    }
}
@media(max-width:400px){
    .rtw_card_body h2 {
        font-size: 1.5rem;
    }
}

