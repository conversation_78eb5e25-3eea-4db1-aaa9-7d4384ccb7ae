<?php
/**
 * Verification Status Checker
 * WooCommerce PDF Invoice & Packing Slip Generator
 * 
 * This script provides detailed status information about the plugin activation
 * and can be used to troubleshoot activation issues.
 * 
 * Usage: 
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: yoursite.com/rtwwcpig-verification-status.php
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Security check - only allow execution if WordPress is loaded
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_config_path = dirname(__FILE__) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress not found. Please place this file in your WordPress root directory.');
    }
}

// Additional security - check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

/**
 * Get comprehensive plugin status
 */
function rtwwcpig_get_comprehensive_status() {
    $status = array();
    
    // Check if plugin is installed
    $plugin_file = WP_PLUGIN_DIR . '/rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php';
    $status['plugin_installed'] = file_exists($plugin_file);
    
    // Check if plugin is active
    $status['plugin_active'] = is_plugin_active('rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php');
    
    // Check verification option
    $verification_data = get_option('rtwwcpig_verification_done', array());
    $status['verification_option_exists'] = !empty($verification_data);
    $status['verification_data'] = $verification_data;
    
    // Check if properly activated
    $status['properly_activated'] = false;
    if (!empty($verification_data) && isset($verification_data['status']) && $verification_data['status'] === true) {
        $status['properly_activated'] = true;
    }
    
    // Check WooCommerce dependency
    $status['woocommerce_active'] = is_plugin_active('woocommerce/woocommerce.php');
    
    // Check plugin constants
    $status['constants_defined'] = array(
        'RTWWCPIG_DIR' => defined('RTWWCPIG_DIR'),
        'RTWWCPIG_URL' => defined('RTWWCPIG_URL'),
        'RTWWCPIG_VERSION' => defined('RTWWCPIG_WOOCOMMERCE_PDF_INVOICE_GENERATOR_VERSION')
    );
    
    // Check plugin classes
    $status['classes_loaded'] = array(
        'Main' => class_exists('Rtwwcpig_Woocommerce_Pdf_Invoice_Generator'),
        'Admin' => class_exists('Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Admin'),
        'Public' => class_exists('Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Public')
    );
    
    return $status;
}

/**
 * Get activation recommendations
 */
function rtwwcpig_get_recommendations($status) {
    $recommendations = array();
    
    if (!$status['plugin_installed']) {
        $recommendations[] = array(
            'type' => 'error',
            'message' => 'Plugin files not found. Please reinstall the plugin.'
        );
    }
    
    if (!$status['plugin_active']) {
        $recommendations[] = array(
            'type' => 'warning',
            'message' => 'Plugin is not active. Please activate it from the WordPress admin.'
        );
    }
    
    if (!$status['woocommerce_active']) {
        $recommendations[] = array(
            'type' => 'warning',
            'message' => 'WooCommerce is not active. This plugin requires WooCommerce to function properly.'
        );
    }
    
    if (!$status['properly_activated']) {
        $recommendations[] = array(
            'type' => 'error',
            'message' => 'Plugin is not properly activated. Use the permanent activation script to fix this.'
        );
    } else {
        $recommendations[] = array(
            'type' => 'success',
            'message' => 'Plugin is properly activated and should be fully functional.'
        );
    }
    
    return $recommendations;
}

$status = rtwwcpig_get_comprehensive_status();
$recommendations = rtwwcpig_get_recommendations($status);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WooCommerce PDF Invoice Generator - Status Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        h1 {
            border-bottom: 3px solid #0073aa;
            padding-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6c757d;
        }
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-badge.success {
            background: #28a745;
            color: white;
        }
        .status-badge.error {
            background: #dc3545;
            color: white;
        }
        .status-badge.warning {
            background: #ffc107;
            color: #212529;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .details-table th,
        .details-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .details-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .recommendation {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .recommendation.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .recommendation.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .recommendation.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .actions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
        }
        .button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WooCommerce PDF Invoice Generator</h1>
        <h2>Status Verification Report</h2>
        
        <div class="status-grid">
            <div class="status-card <?php echo $status['properly_activated'] ? 'success' : 'error'; ?>">
                <h3>Overall Status</h3>
                <div class="status-item">
                    <span>Plugin Activation</span>
                    <span class="status-badge <?php echo $status['properly_activated'] ? 'success' : 'error'; ?>">
                        <?php echo $status['properly_activated'] ? '✅ ACTIVE' : '❌ INACTIVE'; ?>
                    </span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>Quick Status</h3>
                <div class="status-item">
                    <span>Plugin Installed</span>
                    <span class="status-badge <?php echo $status['plugin_installed'] ? 'success' : 'error'; ?>">
                        <?php echo $status['plugin_installed'] ? 'YES' : 'NO'; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span>Plugin Active</span>
                    <span class="status-badge <?php echo $status['plugin_active'] ? 'success' : 'error'; ?>">
                        <?php echo $status['plugin_active'] ? 'YES' : 'NO'; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span>WooCommerce Active</span>
                    <span class="status-badge <?php echo $status['woocommerce_active'] ? 'success' : 'warning'; ?>">
                        <?php echo $status['woocommerce_active'] ? 'YES' : 'NO'; ?>
                    </span>
                </div>
            </div>
        </div>
        
        <h3>📋 Detailed Status Information</h3>
        
        <table class="details-table">
            <tr>
                <th>Component</th>
                <th>Status</th>
                <th>Details</th>
            </tr>
            <tr>
                <td>Plugin Installation</td>
                <td><span class="status-badge <?php echo $status['plugin_installed'] ? 'success' : 'error'; ?>">
                    <?php echo $status['plugin_installed'] ? 'INSTALLED' : 'NOT FOUND'; ?>
                </span></td>
                <td>Plugin files presence check</td>
            </tr>
            <tr>
                <td>Plugin Activation</td>
                <td><span class="status-badge <?php echo $status['plugin_active'] ? 'success' : 'error'; ?>">
                    <?php echo $status['plugin_active'] ? 'ACTIVE' : 'INACTIVE'; ?>
                </span></td>
                <td>WordPress plugin activation status</td>
            </tr>
            <tr>
                <td>License Verification</td>
                <td><span class="status-badge <?php echo $status['properly_activated'] ? 'success' : 'error'; ?>">
                    <?php echo $status['properly_activated'] ? 'VERIFIED' : 'NOT VERIFIED'; ?>
                </span></td>
                <td>Internal license verification status</td>
            </tr>
            <tr>
                <td>WooCommerce Dependency</td>
                <td><span class="status-badge <?php echo $status['woocommerce_active'] ? 'success' : 'warning'; ?>">
                    <?php echo $status['woocommerce_active'] ? 'SATISFIED' : 'MISSING'; ?>
                </span></td>
                <td>Required WooCommerce plugin status</td>
            </tr>
        </table>
        
        <?php if (!empty($status['verification_data'])): ?>
        <h3>🔐 Activation Details</h3>
        <div class="code-block">
            <strong>Purchase Code:</strong> <?php echo esc_html($status['verification_data']['purchase_code'] ?? 'N/A'); ?><br>
            <strong>Status:</strong> <?php echo $status['verification_data']['status'] ? 'true' : 'false'; ?><br>
            <strong>Activated Date:</strong> <?php echo esc_html($status['verification_data']['activated_date'] ?? 'N/A'); ?><br>
            <strong>Activation Method:</strong> <?php echo esc_html($status['verification_data']['activation_method'] ?? 'N/A'); ?>
        </div>
        <?php endif; ?>
        
        <h3>💡 Recommendations</h3>
        <?php foreach ($recommendations as $rec): ?>
            <div class="recommendation <?php echo $rec['type']; ?>">
                <?php echo esc_html($rec['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <div class="actions">
            <h3>🛠️ Available Actions</h3>
            <a href="rtwwcpig-permanent-activation.php" class="button">🚀 Permanent Activation Tool</a>
            <a href="<?php echo admin_url('plugins.php'); ?>" class="button">📋 WordPress Plugins Page</a>
            <a href="<?php echo admin_url('admin.php?page=rtwwcpig-pdf-invoice-settings'); ?>" class="button">⚙️ Plugin Settings</a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
            <p><strong>Report Generated:</strong> <?php echo current_time('Y-m-d H:i:s'); ?></p>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        </div>
    </div>
</body>
</html>
