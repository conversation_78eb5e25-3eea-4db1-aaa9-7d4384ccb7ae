<?php
/**
 * Simple Activation Helper
 * WooCommerce PDF Invoice & Packing Slip Generator
 * 
 * This is a lightweight script that can be included in functions.php
 * or used as a mu-plugin to ensure permanent activation.
 * 
 * Usage Options:
 * 1. Add to functions.php: Copy the activation function to your theme's functions.php
 * 2. Use as mu-plugin: Place this file in wp-content/mu-plugins/
 * 3. Include in plugin: Add require_once to your plugin files
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Simple permanent activation function
 * This function ensures the plugin remains activated
 */
function rtwwcpig_simple_permanent_activation() {
    // Check if we need to activate
    $verification_data = get_option('rtwwcpig_verification_done', array());
    
    if (empty($verification_data) || !isset($verification_data['status']) || $verification_data['status'] !== true) {
        $activation_data = array(
            'purchase_code' => 'PERMANENT_ACTIVATION_HELPER_' . time(),
            'status' => true,
            'activated_date' => current_time('mysql'),
            'activation_method' => 'helper_script'
        );
        
        update_option('rtwwcpig_verification_done', $activation_data);
    }
}

/**
 * Hook the activation function to multiple WordPress actions
 * This ensures activation happens at various points
 */
add_action('init', 'rtwwcpig_simple_permanent_activation', 1);
add_action('admin_init', 'rtwwcpig_simple_permanent_activation', 1);
add_action('plugins_loaded', 'rtwwcpig_simple_permanent_activation', 1);

/**
 * Activation function for functions.php
 * Copy this function to your theme's functions.php file
 */
function rtwwcpig_functions_php_activation() {
    if (!function_exists('get_option')) {
        return; // WordPress not loaded yet
    }
    
    $verification_data = get_option('rtwwcpig_verification_done', array());
    
    if (empty($verification_data) || !isset($verification_data['status']) || $verification_data['status'] !== true) {
        $activation_data = array(
            'purchase_code' => 'PERMANENT_ACTIVATION_FUNCTIONS_' . time(),
            'status' => true,
            'activated_date' => current_time('mysql'),
            'activation_method' => 'functions_php'
        );
        
        update_option('rtwwcpig_verification_done', $activation_data);
    }
}

/**
 * Emergency activation function
 * Call this function manually if needed
 */
function rtwwcpig_emergency_activation() {
    $activation_data = array(
        'purchase_code' => 'EMERGENCY_ACTIVATION_' . time() . '_' . wp_generate_password(10, false),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'emergency_manual'
    );
    
    // Force update even if already exists
    update_option('rtwwcpig_verification_done', $activation_data);
    
    return true;
}

/**
 * Check if plugin is properly activated
 */
function rtwwcpig_is_activated() {
    $verification_data = get_option('rtwwcpig_verification_done', array());
    
    return !empty($verification_data) && 
           isset($verification_data['status']) && 
           $verification_data['status'] === true;
}

/**
 * Get activation status information
 */
function rtwwcpig_get_activation_info() {
    $verification_data = get_option('rtwwcpig_verification_done', array());
    
    if (empty($verification_data)) {
        return array(
            'activated' => false,
            'message' => 'Plugin is not activated'
        );
    }
    
    return array(
        'activated' => isset($verification_data['status']) && $verification_data['status'] === true,
        'purchase_code' => $verification_data['purchase_code'] ?? 'N/A',
        'activated_date' => $verification_data['activated_date'] ?? 'N/A',
        'activation_method' => $verification_data['activation_method'] ?? 'N/A',
        'message' => 'Plugin activation data found'
    );
}

/**
 * Admin notice for activation status (optional)
 * Uncomment the line below to show admin notices
 */
// add_action('admin_notices', 'rtwwcpig_activation_admin_notice');

function rtwwcpig_activation_admin_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $info = rtwwcpig_get_activation_info();
    
    if ($info['activated']) {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>WooCommerce PDF Invoice Generator:</strong> Plugin is permanently activated ✅</p>';
        echo '</div>';
    } else {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>WooCommerce PDF Invoice Generator:</strong> Plugin activation needed ⚠️</p>';
        echo '</div>';
    }
}

/**
 * WordPress CLI command support (if WP-CLI is available)
 */
if (defined('WP_CLI') && WP_CLI) {
    /**
     * Activate WooCommerce PDF Invoice Generator permanently
     * 
     * ## EXAMPLES
     * 
     *     wp eval "rtwwcpig_emergency_activation();"
     *     wp eval "echo rtwwcpig_is_activated() ? 'Activated' : 'Not Activated';"
     */
}

/*
 * USAGE INSTRUCTIONS:
 * 
 * 1. AS MU-PLUGIN:
 *    - Place this file in wp-content/mu-plugins/
 *    - It will automatically activate the plugin
 * 
 * 2. IN FUNCTIONS.PHP:
 *    - Copy the rtwwcpig_functions_php_activation() function to your theme's functions.php
 *    - Add: add_action('init', 'rtwwcpig_functions_php_activation', 1);
 * 
 * 3. MANUAL ACTIVATION:
 *    - Call rtwwcpig_emergency_activation() from anywhere in WordPress
 * 
 * 4. CHECK STATUS:
 *    - Use rtwwcpig_is_activated() to check if plugin is activated
 *    - Use rtwwcpig_get_activation_info() for detailed information
 * 
 * 5. WP-CLI:
 *    - wp eval "rtwwcpig_emergency_activation();"
 *    - wp eval "var_dump(rtwwcpig_get_activation_info());"
 */

// Auto-activate when this file is loaded (if used as mu-plugin)
if (did_action('muplugins_loaded')) {
    rtwwcpig_simple_permanent_activation();
}
