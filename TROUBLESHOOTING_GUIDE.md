# Troubleshooting Guide
## WooCommerce PDF Invoice & Packing Slip Generator - Permanent Activation

### 🔍 Quick Diagnostics

Before diving into specific issues, run these quick checks:

1. **Access Verification Tool**: `yoursite.com/rtwwcpig-verification-status.php`
2. **Check WordPress Admin**: Go to Plugins page and verify plugin is active
3. **Test Basic Function**: Try accessing plugin settings page

### 🚨 Common Issues and Solutions

#### Issue 1: Purchase Code Prompt Still Appears

**Symptoms**:
- Plugin shows purchase code verification screen
- Cannot access plugin settings
- Features are locked

**Diagnosis**:
```php
// Check activation option
$verification = get_option('rtwwcpig_verification_done');
var_dump($verification);
// Should return array with 'status' => true
```

**Solutions**:

**Solution A: Manual Activation Tool**
1. Access `yoursite.com/rtwwcpig-permanent-activation.php`
2. Click "Activate Plugin" button
3. Verify success message
4. Refresh plugin settings page

**Solution B: Database Direct Fix**
```php
// Add to functions.php temporarily
function fix_rtwwcpig_activation() {
    $activation_data = array(
        'purchase_code' => 'MANUAL_FIX_' . time(),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'manual_fix'
    );
    update_option('rtwwcpig_verification_done', $activation_data);
}
add_action('init', 'fix_rtwwcpig_activation', 1);
```

**Solution C: WP-CLI Method**
```bash
wp option update rtwwcpig_verification_done '{"purchase_code":"CLI_FIX_'$(date +%s)'","status":true,"activated_date":"'$(date -u +"%Y-%m-%d %H:%M:%S")'","activation_method":"wp_cli_fix"}'
```

#### Issue 2: Plugin Features Not Working After Update

**Symptoms**:
- Plugin appears active but features don't work
- PDF generation fails
- Admin interface shows errors

**Diagnosis**:
1. Check if plugin files were overwritten during update
2. Verify activation status
3. Check for PHP errors in logs

**Solutions**:

**Solution A: Re-apply Core Modifications**
1. Check if main plugin file still has permanent activation code
2. Re-apply modifications if they were overwritten
3. Use backup files as reference

**Solution B: Force Reactivation**
```php
// Emergency reactivation function
function emergency_rtwwcpig_reactivation() {
    delete_option('rtwwcpig_verification_done');
    $activation_data = array(
        'purchase_code' => 'EMERGENCY_' . time() . '_' . wp_generate_password(10, false),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'emergency_reactivation'
    );
    update_option('rtwwcpig_verification_done', $activation_data);
}
// Call this function once
emergency_rtwwcpig_reactivation();
```

#### Issue 3: Plugin Not Found or Deactivated

**Symptoms**:
- Plugin missing from plugins list
- WordPress shows plugin as deactivated
- Plugin files appear to be missing

**Diagnosis**:
1. Check if plugin directory exists
2. Verify file permissions
3. Check for file corruption

**Solutions**:

**Solution A: Reinstall Plugin**
1. Download fresh plugin files
2. Upload to plugins directory
3. Reactivate plugin
4. Run permanent activation tool

**Solution B: File Permission Fix**
```bash
# Fix file permissions
chmod -R 755 wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/
chown -R www-data:www-data wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/
```

#### Issue 4: WooCommerce Dependency Error

**Symptoms**:
- Plugin shows WooCommerce not activated error
- Plugin automatically deactivates
- Dependency warning messages

**Diagnosis**:
```php
// Check WooCommerce status
$wc_active = is_plugin_active('woocommerce/woocommerce.php');
echo $wc_active ? 'WooCommerce is active' : 'WooCommerce is NOT active';
```

**Solutions**:

**Solution A: Activate WooCommerce**
1. Go to WordPress Admin → Plugins
2. Find WooCommerce and activate it
3. Verify PDF Invoice plugin reactivates

**Solution B: Force Activation Despite Dependency**
```php
// Temporarily bypass WooCommerce check (use with caution)
add_filter('pre_option_active_plugins', function($plugins) {
    if (!in_array('woocommerce/woocommerce.php', $plugins)) {
        $plugins[] = 'woocommerce/woocommerce.php';
    }
    return $plugins;
});
```

#### Issue 5: PDF Generation Fails

**Symptoms**:
- Cannot generate invoices or packing slips
- PDF download links don't work
- Error messages during PDF creation

**Diagnosis**:
1. Check file permissions on upload directories
2. Verify mPDF library is intact
3. Check PHP memory limits

**Solutions**:

**Solution A: Directory Permissions**
```bash
# Create and set permissions for PDF directories
mkdir -p wp-content/uploads/rtwwcpig-pdf-invoice/
chmod 755 wp-content/uploads/rtwwcpig-pdf-invoice/
chown www-data:www-data wp-content/uploads/rtwwcpig-pdf-invoice/
```

**Solution B: PHP Configuration**
```php
// Add to wp-config.php
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
```

**Solution C: mPDF Library Check**
```php
// Verify mPDF is available
if (class_exists('Mpdf\Mpdf')) {
    echo 'mPDF library is available';
} else {
    echo 'mPDF library is missing - reinstall plugin';
}
```

### 🔧 Advanced Troubleshooting

#### Debug Mode Activation

Enable WordPress debug mode for detailed error information:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### Plugin Conflict Detection

**Method 1: Plugin Deactivation Test**
1. Deactivate all other plugins except WooCommerce and PDF Invoice
2. Test functionality
3. Reactivate plugins one by one to identify conflicts

**Method 2: Theme Conflict Test**
1. Switch to default WordPress theme
2. Test plugin functionality
3. If it works, the issue is theme-related

#### Database Troubleshooting

**Check Plugin Options**:
```sql
-- Check all plugin-related options
SELECT * FROM wp_options WHERE option_name LIKE '%rtwwcpig%';

-- Specifically check activation option
SELECT * FROM wp_options WHERE option_name = 'rtwwcpig_verification_done';
```

**Reset Plugin Options** (if needed):
```sql
-- CAUTION: This will reset all plugin settings
DELETE FROM wp_options WHERE option_name LIKE '%rtwwcpig%';
```

### 🛠️ Recovery Procedures

#### Complete Reset and Reinstall

**Step 1: Backup Current State**
```bash
# Backup plugin directory
cp -r wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/ backup_before_reset/

# Backup database options
wp db export --tables=wp_options backup_options.sql
```

**Step 2: Clean Removal**
```bash
# Remove plugin files
rm -rf wp-content/plugins/rtwwcpig-woocommerce-pdf-invoice-generator/

# Remove database options
wp option delete rtwwcpig_verification_done
```

**Step 3: Fresh Installation**
1. Upload fresh plugin files
2. Activate plugin in WordPress admin
3. Apply permanent activation modifications
4. Run activation tools

#### Rollback to Original Plugin

If you need to revert to the original licensing system:

**Step 1: Restore Original Files**
```bash
# Restore from backup
cp rtwwcpig-woocommerce-pdf-invoice-generator.php.backup rtwwcpig-woocommerce-pdf-invoice-generator.php
```

**Step 2: Remove Activation Data**
```php
// Remove permanent activation
delete_option('rtwwcpig_verification_done');
```

**Step 3: Clean Up**
```bash
# Remove activation tools
rm rtwwcpig-permanent-activation.php
rm rtwwcpig-verification-status.php
rm rtwwcpig-activation-helper.php
```

### 📊 Diagnostic Tools

#### Custom Diagnostic Script

Create `rtwwcpig-diagnostics.php`:

```php
<?php
// Load WordPress
require_once('wp-config.php');

echo "<h1>PDF Invoice Plugin Diagnostics</h1>";

// Check plugin installation
$plugin_file = WP_PLUGIN_DIR . '/rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php';
echo "<p>Plugin Installed: " . (file_exists($plugin_file) ? 'YES' : 'NO') . "</p>";

// Check plugin activation
$active = is_plugin_active('rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php');
echo "<p>Plugin Active: " . ($active ? 'YES' : 'NO') . "</p>";

// Check license verification
$verification = get_option('rtwwcpig_verification_done', array());
echo "<p>License Verified: " . (!empty($verification) && $verification['status'] === true ? 'YES' : 'NO') . "</p>";

// Check WooCommerce
$wc_active = is_plugin_active('woocommerce/woocommerce.php');
echo "<p>WooCommerce Active: " . ($wc_active ? 'YES' : 'NO') . "</p>";

// Check directories
$pdf_dir = WP_CONTENT_DIR . '/uploads/rtwwcpig-pdf-invoice/';
echo "<p>PDF Directory Exists: " . (is_dir($pdf_dir) ? 'YES' : 'NO') . "</p>";
echo "<p>PDF Directory Writable: " . (is_writable($pdf_dir) ? 'YES' : 'NO') . "</p>";

// Check PHP requirements
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . "</p>";

// Check classes
echo "<p>Main Class Loaded: " . (class_exists('Rtwwcpig_Woocommerce_Pdf_Invoice_Generator') ? 'YES' : 'NO') . "</p>";
echo "<p>mPDF Available: " . (class_exists('Mpdf\Mpdf') ? 'YES' : 'NO') . "</p>";

if (!empty($verification)) {
    echo "<h2>Activation Details</h2>";
    echo "<pre>" . print_r($verification, true) . "</pre>";
}
?>
```

#### WP-CLI Diagnostic Commands

```bash
# Check plugin status
wp plugin status rtwwcpig-woocommerce-pdf-invoice-generator

# Check activation option
wp option get rtwwcpig_verification_done

# Check all plugin options
wp option list --search="rtwwcpig*"

# Test plugin activation
wp plugin activate rtwwcpig-woocommerce-pdf-invoice-generator

# Check for errors
wp plugin status --format=json | jq '.[] | select(.name=="rtwwcpig-woocommerce-pdf-invoice-generator")'
```

### 📞 Getting Help

#### Information to Gather Before Seeking Help

1. **WordPress Version**: `wp --version`
2. **PHP Version**: `php --version`
3. **Plugin Version**: Check plugin file header
4. **Error Messages**: Copy exact error text
5. **Activation Status**: Output from verification tool
6. **Recent Changes**: Any recent updates or modifications

#### Log Files to Check

1. **WordPress Debug Log**: `wp-content/debug.log`
2. **Server Error Log**: Usually in `/var/log/apache2/error.log` or `/var/log/nginx/error.log`
3. **PHP Error Log**: Location varies by server configuration

#### Quick Fix Checklist

Before seeking help, try these quick fixes:

- [ ] Run the permanent activation tool
- [ ] Check file permissions
- [ ] Verify WooCommerce is active
- [ ] Clear all caches
- [ ] Deactivate and reactivate the plugin
- [ ] Check for plugin conflicts
- [ ] Review error logs

This troubleshooting guide should resolve most common issues with the permanent activation solution.
