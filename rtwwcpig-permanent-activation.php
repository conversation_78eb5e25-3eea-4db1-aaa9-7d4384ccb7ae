<?php
/**
 * Standalone Permanent Activation Script
 * WooCommerce PDF Invoice & Packing Slip Generator
 * 
 * This script provides manual activation capabilities for the plugin
 * and can be used to restore activation if it's lost after updates.
 * 
 * Usage: 
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: yoursite.com/rtwwcpig-permanent-activation.php
 * 3. Follow the on-screen instructions
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Security check - only allow execution if WordPress is loaded
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_config_path = dirname(__FILE__) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress not found. Please place this file in your WordPress root directory.');
    }
}

// Additional security - check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

/**
 * Main activation function
 */
function rtwwcpig_manual_permanent_activation() {
    $verification_data = array(
        'purchase_code' => 'PERMANENT_ACTIVATION_MANUAL_' . time() . '_' . wp_generate_password(16, false),
        'status' => true,
        'activated_date' => current_time('mysql'),
        'activation_method' => 'manual_standalone_script',
        'script_version' => '1.0.0'
    );
    
    $result = update_option('rtwwcpig_verification_done', $verification_data);
    
    return $result;
}

/**
 * Check current activation status
 */
function rtwwcpig_check_activation_status() {
    $verification_data = get_option('rtwwcpig_verification_done', array());
    
    if (empty($verification_data)) {
        return array(
            'status' => false,
            'message' => 'Plugin is not activated'
        );
    }
    
    if (!isset($verification_data['status']) || $verification_data['status'] !== true) {
        return array(
            'status' => false,
            'message' => 'Plugin activation is invalid'
        );
    }
    
    return array(
        'status' => true,
        'message' => 'Plugin is permanently activated',
        'data' => $verification_data
    );
}

/**
 * Force reactivation (useful after plugin updates)
 */
function rtwwcpig_force_reactivation() {
    // Delete existing activation
    delete_option('rtwwcpig_verification_done');
    
    // Create new activation
    return rtwwcpig_manual_permanent_activation();
}

// Handle form submissions
$action_result = '';
$current_status = rtwwcpig_check_activation_status();

if (isset($_POST['action'])) {
    $nonce = $_POST['_wpnonce'] ?? '';
    
    if (!wp_verify_nonce($nonce, 'rtwwcpig_activation_action')) {
        $action_result = '<div class="error">Security check failed. Please try again.</div>';
    } else {
        switch ($_POST['action']) {
            case 'activate':
                if (rtwwcpig_manual_permanent_activation()) {
                    $action_result = '<div class="success">✅ Plugin activated successfully!</div>';
                    $current_status = rtwwcpig_check_activation_status();
                } else {
                    $action_result = '<div class="error">❌ Activation failed. Please try again.</div>';
                }
                break;
                
            case 'reactivate':
                if (rtwwcpig_force_reactivation()) {
                    $action_result = '<div class="success">✅ Plugin reactivated successfully!</div>';
                    $current_status = rtwwcpig_check_activation_status();
                } else {
                    $action_result = '<div class="error">❌ Reactivation failed. Please try again.</div>';
                }
                break;
                
            case 'check':
                $current_status = rtwwcpig_check_activation_status();
                $action_result = '<div class="info">🔍 Status check completed.</div>';
                break;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WooCommerce PDF Invoice Generator - Permanent Activation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #0073aa;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .button {
            background: #0073aa;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a87;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #545b62;
        }
        .details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WooCommerce PDF Invoice Generator</h1>
        <h2>Permanent Activation Tool</h2>
        
        <?php echo $action_result; ?>
        
        <div class="status <?php echo $current_status['status'] ? 'active' : 'inactive'; ?>">
            <strong>Current Status:</strong> <?php echo $current_status['message']; ?>
        </div>
        
        <?php if ($current_status['status'] && isset($current_status['data'])): ?>
            <div class="details">
                <strong>Activation Details:</strong><br>
                Purchase Code: <?php echo esc_html($current_status['data']['purchase_code'] ?? 'N/A'); ?><br>
                Activated Date: <?php echo esc_html($current_status['data']['activated_date'] ?? 'N/A'); ?><br>
                Activation Method: <?php echo esc_html($current_status['data']['activation_method'] ?? 'N/A'); ?>
            </div>
        <?php endif; ?>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This tool provides permanent activation for the WooCommerce PDF Invoice & Packing Slip Generator plugin. 
            Use responsibly and ensure you have proper rights to use this plugin.
        </div>
        
        <form method="post">
            <?php wp_nonce_field('rtwwcpig_activation_action'); ?>
            
            <?php if (!$current_status['status']): ?>
                <button type="submit" name="action" value="activate" class="button">
                    🚀 Activate Plugin
                </button>
            <?php else: ?>
                <button type="submit" name="action" value="reactivate" class="button secondary">
                    🔄 Force Reactivation
                </button>
            <?php endif; ?>
            
            <button type="submit" name="action" value="check" class="button secondary">
                🔍 Check Status
            </button>
        </form>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
            <strong>Instructions:</strong>
            <ul>
                <li><strong>Activate Plugin:</strong> Sets up permanent activation for the first time</li>
                <li><strong>Force Reactivation:</strong> Use this after plugin updates if activation is lost</li>
                <li><strong>Check Status:</strong> Verify current activation status</li>
            </ul>
            
            <p><strong>Security Note:</strong> Delete this file after use for security purposes.</p>
        </div>
    </div>
</body>
</html>
