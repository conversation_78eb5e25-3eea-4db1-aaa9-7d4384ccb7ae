{"name": "myclabs/deep-copy", "type": "library", "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "license": "MIT", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "autoload-dev": {"psr-4": {"DeepCopy\\": "fixtures/", "DeepCopyTest\\": "tests/DeepCopyTest/"}}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "config": {"sort-packages": true}}