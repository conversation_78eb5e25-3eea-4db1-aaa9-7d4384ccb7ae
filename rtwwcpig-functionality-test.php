<?php
/**
 * Functionality Test Suite
 * WooCommerce PDF Invoice & Packing Slip Generator
 * 
 * This script performs comprehensive testing of the plugin functionality
 * to ensure all features work correctly after permanent activation.
 * 
 * Usage: 
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: yoursite.com/rtwwcpig-functionality-test.php
 * 3. Review the test results
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Security check - only allow execution if WordPress is loaded
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_config_path = dirname(__FILE__) . '/wp-config.php';
    if (file_exists($wp_config_path)) {
        require_once($wp_config_path);
    } else {
        die('WordPress not found. Please place this file in your WordPress root directory.');
    }
}

// Additional security - check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

/**
 * Test Suite Class
 */
class RtwwcpigFunctionalityTest {
    
    private $test_results = array();
    private $overall_status = true;
    
    public function __construct() {
        $this->run_all_tests();
    }
    
    /**
     * Run all tests
     */
    public function run_all_tests() {
        $this->test_plugin_installation();
        $this->test_plugin_activation();
        $this->test_license_verification();
        $this->test_woocommerce_dependency();
        $this->test_class_loading();
        $this->test_admin_interface();
        $this->test_database_options();
        $this->test_file_permissions();
        $this->test_pdf_generation_capability();
        $this->test_hooks_and_filters();
    }
    
    /**
     * Add test result
     */
    private function add_test($name, $status, $message, $details = '') {
        $this->test_results[] = array(
            'name' => $name,
            'status' => $status,
            'message' => $message,
            'details' => $details
        );
        
        if (!$status) {
            $this->overall_status = false;
        }
    }
    
    /**
     * Test plugin installation
     */
    private function test_plugin_installation() {
        $plugin_file = WP_PLUGIN_DIR . '/rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php';
        $exists = file_exists($plugin_file);
        
        $this->add_test(
            'Plugin Installation',
            $exists,
            $exists ? 'Plugin files found' : 'Plugin files missing',
            $exists ? 'Plugin directory: ' . dirname($plugin_file) : 'Expected location: ' . $plugin_file
        );
    }
    
    /**
     * Test plugin activation
     */
    private function test_plugin_activation() {
        $active = is_plugin_active('rtwwcpig-woocommerce-pdf-invoice-generator/rtwwcpig-woocommerce-pdf-invoice-generator.php');
        
        $this->add_test(
            'Plugin Activation',
            $active,
            $active ? 'Plugin is active' : 'Plugin is not active',
            $active ? 'WordPress recognizes plugin as active' : 'Plugin needs to be activated in WordPress admin'
        );
    }
    
    /**
     * Test license verification
     */
    private function test_license_verification() {
        $verification = get_option('rtwwcpig_verification_done', array());
        $valid = !empty($verification) && isset($verification['status']) && $verification['status'] === true;
        
        $details = '';
        if ($valid) {
            $details = 'Purchase Code: ' . ($verification['purchase_code'] ?? 'N/A') . ', ';
            $details .= 'Method: ' . ($verification['activation_method'] ?? 'N/A') . ', ';
            $details .= 'Date: ' . ($verification['activated_date'] ?? 'N/A');
        }
        
        $this->add_test(
            'License Verification',
            $valid,
            $valid ? 'License is verified and active' : 'License verification failed',
            $valid ? $details : 'No valid license data found in database'
        );
    }
    
    /**
     * Test WooCommerce dependency
     */
    private function test_woocommerce_dependency() {
        $wc_active = is_plugin_active('woocommerce/woocommerce.php');
        
        $this->add_test(
            'WooCommerce Dependency',
            $wc_active,
            $wc_active ? 'WooCommerce is active' : 'WooCommerce is not active',
            $wc_active ? 'Required dependency satisfied' : 'WooCommerce must be installed and activated'
        );
    }
    
    /**
     * Test class loading
     */
    private function test_class_loading() {
        $classes = array(
            'Rtwwcpig_Woocommerce_Pdf_Invoice_Generator' => 'Main plugin class',
            'Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Admin' => 'Admin class',
            'Rtwwcpig_Woocommerce_Pdf_Invoice_Generator_Public' => 'Public class',
            'Mpdf\Mpdf' => 'mPDF library class'
        );
        
        $all_loaded = true;
        $details = array();
        
        foreach ($classes as $class => $description) {
            $loaded = class_exists($class);
            $details[] = $description . ': ' . ($loaded ? 'LOADED' : 'MISSING');
            if (!$loaded) {
                $all_loaded = false;
            }
        }
        
        $this->add_test(
            'Class Loading',
            $all_loaded,
            $all_loaded ? 'All required classes loaded' : 'Some classes missing',
            implode(', ', $details)
        );
    }
    
    /**
     * Test admin interface accessibility
     */
    private function test_admin_interface() {
        // Check if admin menu exists
        global $menu, $submenu;
        $menu_exists = false;
        
        if (is_array($menu)) {
            foreach ($menu as $menu_item) {
                if (isset($menu_item[2]) && strpos($menu_item[2], 'rtwwcpig') !== false) {
                    $menu_exists = true;
                    break;
                }
            }
        }
        
        $this->add_test(
            'Admin Interface',
            $menu_exists,
            $menu_exists ? 'Admin menu accessible' : 'Admin menu not found',
            $menu_exists ? 'Plugin settings should be accessible' : 'Check if plugin is properly activated'
        );
    }
    
    /**
     * Test database options
     */
    private function test_database_options() {
        $options_to_check = array(
            'rtwwcpig_verification_done',
            'rtwwcpig_regular_invoice',
            'rtwwcpig_proforma_invoice'
        );
        
        $options_found = 0;
        $details = array();
        
        foreach ($options_to_check as $option) {
            $value = get_option($option, null);
            $exists = $value !== null;
            $options_found += $exists ? 1 : 0;
            $details[] = $option . ': ' . ($exists ? 'EXISTS' : 'MISSING');
        }
        
        $success = $options_found >= 1; // At least verification option should exist
        
        $this->add_test(
            'Database Options',
            $success,
            $success ? 'Plugin options found in database' : 'Plugin options missing',
            implode(', ', $details)
        );
    }
    
    /**
     * Test file permissions
     */
    private function test_file_permissions() {
        $directories_to_check = array(
            WP_CONTENT_DIR . '/uploads/',
            WP_CONTENT_DIR . '/uploads/rtwwcpig-pdf-invoice/',
            WP_CONTENT_DIR . '/uploads/rtwwcpig-pdf-invoice/rtwwcpig_pckng_slip/',
        );
        
        $all_writable = true;
        $details = array();
        
        foreach ($directories_to_check as $dir) {
            if (!is_dir($dir)) {
                wp_mkdir_p($dir);
            }
            
            $writable = is_writable($dir);
            $details[] = basename($dir) . ': ' . ($writable ? 'WRITABLE' : 'NOT WRITABLE');
            
            if (!$writable) {
                $all_writable = false;
            }
        }
        
        $this->add_test(
            'File Permissions',
            $all_writable,
            $all_writable ? 'All directories writable' : 'Some directories not writable',
            implode(', ', $details)
        );
    }
    
    /**
     * Test PDF generation capability
     */
    private function test_pdf_generation_capability() {
        $mpdf_available = class_exists('Mpdf\Mpdf');
        $php_requirements = true;
        $details = array();
        
        // Check PHP extensions
        $required_extensions = array('gd', 'mbstring');
        foreach ($required_extensions as $ext) {
            $loaded = extension_loaded($ext);
            $details[] = $ext . ': ' . ($loaded ? 'LOADED' : 'MISSING');
            if (!$loaded) {
                $php_requirements = false;
            }
        }
        
        $success = $mpdf_available && $php_requirements;
        
        $this->add_test(
            'PDF Generation Capability',
            $success,
            $success ? 'PDF generation should work' : 'PDF generation may fail',
            'mPDF: ' . ($mpdf_available ? 'AVAILABLE' : 'MISSING') . ', ' . implode(', ', $details)
        );
    }
    
    /**
     * Test hooks and filters
     */
    private function test_hooks_and_filters() {
        global $wp_filter;
        
        $hooks_to_check = array(
            'plugins_loaded',
            'init',
            'admin_init',
            'wp_ajax_rtwwcpig_verify_purchase_code'
        );
        
        $hooks_found = 0;
        $details = array();
        
        foreach ($hooks_to_check as $hook) {
            $has_callbacks = isset($wp_filter[$hook]) && !empty($wp_filter[$hook]->callbacks);
            $hooks_found += $has_callbacks ? 1 : 0;
            $details[] = $hook . ': ' . ($has_callbacks ? 'HAS CALLBACKS' : 'NO CALLBACKS');
        }
        
        $success = $hooks_found >= 2; // At least some hooks should have callbacks
        
        $this->add_test(
            'Hooks and Filters',
            $success,
            $success ? 'WordPress hooks functioning' : 'Hook system may have issues',
            implode(', ', $details)
        );
    }
    
    /**
     * Get test results
     */
    public function get_results() {
        return array(
            'overall_status' => $this->overall_status,
            'tests' => $this->test_results,
            'summary' => $this->get_summary()
        );
    }
    
    /**
     * Get test summary
     */
    private function get_summary() {
        $total = count($this->test_results);
        $passed = 0;
        $failed = 0;
        
        foreach ($this->test_results as $test) {
            if ($test['status']) {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        return array(
            'total' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
        );
    }
}

// Run tests
$test_suite = new RtwwcpigFunctionalityTest();
$results = $test_suite->get_results();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WooCommerce PDF Invoice Generator - Functionality Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #0073aa;
            padding-bottom: 10px;
        }
        .summary {
            background: <?php echo $results['overall_status'] ? '#d4edda' : '#f8d7da'; ?>;
            color: <?php echo $results['overall_status'] ? '#155724' : '#721c24'; ?>;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid <?php echo $results['overall_status'] ? '#c3e6cb' : '#f5c6cb'; ?>;
        }
        .summary h2 {
            margin-top: 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .test-results {
            margin: 30px 0;
        }
        .test-item {
            background: #f8f9fa;
            margin: 10px 0;
            border-radius: 5px;
            overflow: hidden;
            border-left: 4px solid #6c757d;
        }
        .test-item.pass {
            border-left-color: #28a745;
        }
        .test-item.fail {
            border-left-color: #dc3545;
        }
        .test-header {
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-name {
            font-weight: bold;
            font-size: 16px;
        }
        .test-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .test-status.pass {
            background: #28a745;
            color: white;
        }
        .test-status.fail {
            background: #dc3545;
            color: white;
        }
        .test-details {
            padding: 15px 20px;
            font-size: 14px;
            color: #666;
        }
        .test-message {
            margin-bottom: 10px;
            font-weight: 500;
        }
        .actions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
        }
        .button:hover {
            background: #005a87;
        }
        .button.secondary {
            background: #6c757d;
        }
        .recommendations {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WooCommerce PDF Invoice Generator</h1>
        <h2>Functionality Test Report</h2>
        
        <div class="summary">
            <h2><?php echo $results['overall_status'] ? '✅ All Tests Passed' : '❌ Some Tests Failed'; ?></h2>
            <p><?php echo $results['overall_status'] ? 'The plugin appears to be functioning correctly.' : 'Some issues were detected that may affect functionality.'; ?></p>
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-value"><?php echo $results['summary']['total']; ?></div>
                <div>Total Tests</div>
            </div>
            <div class="stat">
                <div class="stat-value" style="color: #28a745;"><?php echo $results['summary']['passed']; ?></div>
                <div>Passed</div>
            </div>
            <div class="stat">
                <div class="stat-value" style="color: #dc3545;"><?php echo $results['summary']['failed']; ?></div>
                <div>Failed</div>
            </div>
            <div class="stat">
                <div class="stat-value"><?php echo $results['summary']['success_rate']; ?>%</div>
                <div>Success Rate</div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>📋 Detailed Test Results</h3>
            
            <?php foreach ($results['tests'] as $test): ?>
                <div class="test-item <?php echo $test['status'] ? 'pass' : 'fail'; ?>">
                    <div class="test-header">
                        <div class="test-name"><?php echo esc_html($test['name']); ?></div>
                        <div class="test-status <?php echo $test['status'] ? 'pass' : 'fail'; ?>">
                            <?php echo $test['status'] ? 'PASS' : 'FAIL'; ?>
                        </div>
                    </div>
                    <div class="test-details">
                        <div class="test-message"><?php echo esc_html($test['message']); ?></div>
                        <?php if (!empty($test['details'])): ?>
                            <div><strong>Details:</strong> <?php echo esc_html($test['details']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (!$results['overall_status']): ?>
            <div class="recommendations">
                <h3>💡 Recommendations</h3>
                <ul>
                    <li>If license verification failed, run the permanent activation tool</li>
                    <li>If WooCommerce is not active, activate it from the plugins page</li>
                    <li>If classes are missing, reinstall the plugin</li>
                    <li>If directories are not writable, check file permissions</li>
                    <li>If PHP extensions are missing, contact your hosting provider</li>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="actions">
            <h3>🛠️ Available Actions</h3>
            <a href="rtwwcpig-permanent-activation.php" class="button">🚀 Permanent Activation Tool</a>
            <a href="rtwwcpig-verification-status.php" class="button">🔍 Status Verification</a>
            <a href="<?php echo admin_url('admin.php?page=rtwwcpig-pdf-invoice-settings'); ?>" class="button">⚙️ Plugin Settings</a>
            <a href="<?php echo admin_url('plugins.php'); ?>" class="button secondary">📋 WordPress Plugins</a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
            <p><strong>Test Report Generated:</strong> <?php echo current_time('Y-m-d H:i:s'); ?></p>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Plugin Version:</strong> <?php echo defined('RTWWCPIG_WOOCOMMERCE_PDF_INVOICE_GENERATOR_VERSION') ? RTWWCPIG_WOOCOMMERCE_PDF_INVOICE_GENERATOR_VERSION : 'Unknown'; ?></p>
        </div>
    </div>
</body>
</html>
